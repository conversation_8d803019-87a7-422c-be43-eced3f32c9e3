#!/usr/bin/env python3
import json
import re

def parse_escaped_json_string(json_str):
    """Parse a JSON string that has been escaped and stored as a string."""
    try:
        # Remove the outer quotes and unescape the JSON
        if json_str.startswith('"') and json_str.endswith('"'):
            json_str = json_str[1:-1]
        
        # Replace escaped quotes
        json_str = json_str.replace('\\"', '"')
        json_str = json_str.replace('\\/', '/')
        
        # Parse the JSON
        return json.loads(json_str)
    except (json.JSONDecodeError, ValueError) as e:
        print(f"Error parsing JSON string: {e}")
        print(f"String was: {json_str[:100]}...")
        return []

def parse_jobs_string(jobs_str):
    """Parse the jobs string which contains multiple JSON objects as strings."""
    try:
        # Remove outer braces and quotes
        if jobs_str.startswith('"{') and jobs_str.endswith('}"'):
            jobs_str = jobs_str[1:-1]
        elif jobs_str.startswith('{') and jobs_str.endswith('}'):
            jobs_str = jobs_str[1:-1]
        
        # Split by "},"{" pattern to get individual job strings
        job_strings = re.split(r'",\s*"', jobs_str)
        
        jobs = []
        for job_str in job_strings:
            # Clean up the job string
            job_str = job_str.strip()
            if job_str.startswith('"'):
                job_str = job_str[1:]
            if job_str.endswith('"'):
                job_str = job_str[:-1]
            
            # Replace escaped quotes
            job_str = job_str.replace('\\"', '"')
            job_str = job_str.replace('\\/', '/')
            
            # Add braces if missing
            if not job_str.startswith('{'):
                job_str = '{' + job_str
            if not job_str.endswith('}'):
                job_str = job_str + '}'
            
            try:
                job = json.loads(job_str)
                jobs.append(job)
            except json.JSONDecodeError as e:
                print(f"Error parsing job: {e}")
                print(f"Job string was: {job_str}")
                continue
        
        return jobs
    except Exception as e:
        print(f"Error parsing jobs string: {e}")
        return []

def fix_json_file(input_file, output_file, max_records=30):
    """Fix the JSON file structure and limit to max_records."""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    try:
        # Parse the original JSON
        data = json.loads(content)
        
        if not isinstance(data, list):
            print("Error: JSON root is not an array")
            return
        
        # Limit to first max_records
        limited_data = data[:max_records]
        
        # Fix each record
        fixed_data = []
        for i, record in enumerate(limited_data):
            print(f"Processing record {i+1}/{len(limited_data)}")
            
            fixed_record = record.copy()
            
            # Fix files_json field
            if 'files_json' in record and isinstance(record['files_json'], str):
                fixed_record['files_json'] = parse_escaped_json_string(record['files_json'])
            
            # Fix jobs field
            if 'jobs' in record and isinstance(record['jobs'], str):
                fixed_record['jobs'] = parse_jobs_string(record['jobs'])
            
            fixed_data.append(fixed_record)
        
        # Write the fixed JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(fixed_data, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully processed {len(fixed_data)} records and saved to {output_file}")
        
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    fix_json_file("Untitled-1.json", "Untitled-1-fixed.json", 30)
