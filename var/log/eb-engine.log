2025/06/17 05:48:52.220432 [INFO] Starting...
2025/06/17 05:48:52.220688 [INFO] Starting EBPlatform-PlatformEngine
2025/06/17 05:48:52.220746 [INFO] reading event message file
2025/06/17 05:48:52.221168 [INFO] Engine received EB command cfn-hup-exec

2025/06/17 05:48:52.295238 [INFO] Running command /bin/sh -c /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:755876169388:stack/awseb-e-cmtzvq7zyr-stack/782b3560-e005-11ee-bfb3-025fd4fd6033 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/17 05:48:52.591536 [INFO] Running command /bin/sh -c /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:755876169388:stack/awseb-e-cmtzvq7zyr-stack/782b3560-e005-11ee-bfb3-025fd4fd6033 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/17 05:48:52.839216 [INFO] checking whether command tail-log is applicable to this instance...
2025/06/17 05:48:52.839231 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/17 05:48:52.839234 [INFO] Engine command: (tail-log)

2025/06/17 05:48:52.839274 [INFO] Executing instruction: GetTailLogs
2025/06/17 05:48:52.839277 [INFO] Tail Logs...
2025/06/17 05:48:52.839560 [INFO] Running command /bin/sh -c tail -n 100 /var/log/eb-hooks.log
2025/06/17 05:48:52.841994 [INFO] Running command /bin/sh -c tail -n 100 /var/log/nginx/access.log
2025/06/17 05:48:52.845726 [INFO] Running command /bin/sh -c tail -n 100 /var/log/nginx/error.log
2025/06/17 05:48:52.847690 [INFO] Running command /bin/sh -c tail -n 100 /var/log/secure
2025/06/17 05:48:52.851028 [INFO] Running command /bin/sh -c tail -n 100 /var/log/web.stdout.log
2025/06/17 05:48:52.876394 [INFO] Running command /bin/sh -c tail -n 100 /var/log/eb-engine.log
2025/06/17 05:48:52.975356 [INFO] Executing cleanup logic
2025/06/17 05:48:52.975427 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment completed successfully.","timestamp":1750139332975,"severity":"INFO"}]}]}

2025/06/17 05:48:52.975442 [INFO] Platform Engine finished execution on command: tail-log

2025/06/17 05:50:21.607794 [INFO] Starting...
2025/06/17 05:50:21.607838 [INFO] Starting EBPlatform-PlatformEngine
2025/06/17 05:50:21.607858 [INFO] reading event message file
2025/06/17 05:50:21.608457 [INFO] Engine received EB command cfn-hup-exec

2025/06/17 05:50:21.671660 [INFO] Running command /bin/sh -c /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:755876169388:stack/awseb-e-cmtzvq7zyr-stack/782b3560-e005-11ee-bfb3-025fd4fd6033 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/17 05:50:21.900214 [INFO] Running command /bin/sh -c /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:755876169388:stack/awseb-e-cmtzvq7zyr-stack/782b3560-e005-11ee-bfb3-025fd4fd6033 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/17 05:50:22.126314 [INFO] checking whether command tail-log is applicable to this instance...
2025/06/17 05:50:22.126328 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/17 05:50:22.126331 [INFO] Engine command: (tail-log)

2025/06/17 05:50:22.126375 [INFO] Executing instruction: GetTailLogs
2025/06/17 05:50:22.126379 [INFO] Tail Logs...
2025/06/17 05:50:22.126651 [INFO] Running command /bin/sh -c tail -n 100 /var/log/secure
2025/06/17 05:50:22.128311 [INFO] Running command /bin/sh -c tail -n 100 /var/log/web.stdout.log
2025/06/17 05:50:22.141371 [INFO] Running command /bin/sh -c tail -n 100 /var/log/eb-engine.log
2025/06/17 05:50:22.144881 [INFO] Running command /bin/sh -c tail -n 100 /var/log/eb-hooks.log
2025/06/17 05:50:22.148187 [INFO] Running command /bin/sh -c tail -n 100 /var/log/nginx/access.log
2025/06/17 05:50:22.153842 [INFO] Running command /bin/sh -c tail -n 100 /var/log/nginx/error.log
2025/06/17 05:50:22.246550 [INFO] Executing cleanup logic
2025/06/17 05:50:22.246621 [INFO] CommandService Response: {"status":"SUCCESS","api_version":"1.0","results":[{"status":"SUCCESS","msg":"Engine execution has succeeded.","returncode":0,"events":[{"msg":"Instance deployment completed successfully.","timestamp":1750139422246,"severity":"INFO"}]}]}

2025/06/17 05:50:22.246635 [INFO] Platform Engine finished execution on command: tail-log

2025/06/17 05:52:11.169519 [INFO] Starting...
2025/06/17 05:52:11.170018 [INFO] Starting EBPlatform-PlatformEngine
2025/06/17 05:52:11.170190 [INFO] reading event message file
2025/06/17 05:52:11.170373 [INFO] Engine received EB command cfn-hup-exec

2025/06/17 05:52:11.234606 [INFO] Running command /bin/sh -c /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:755876169388:stack/awseb-e-cmtzvq7zyr-stack/782b3560-e005-11ee-bfb3-025fd4fd6033 -r AWSEBAutoScalingGroup --region us-west-2
2025/06/17 05:52:11.520318 [INFO] Running command /bin/sh -c /opt/aws/bin/cfn-get-metadata -s arn:aws:cloudformation:us-west-2:755876169388:stack/awseb-e-cmtzvq7zyr-stack/782b3560-e005-11ee-bfb3-025fd4fd6033 -r AWSEBBeanstalkMetadata --region us-west-2
2025/06/17 05:52:11.815898 [INFO] checking whether command bundle-log is applicable to this instance...
2025/06/17 05:52:11.815912 [INFO] this command is applicable to the instance, thus instance should execute command
2025/06/17 05:52:11.815915 [INFO] Engine command: (bundle-log)

2025/06/17 05:52:11.815955 [INFO] Executing instruction: GetBundleLogs
2025/06/17 05:52:11.815958 [INFO] Bundle Logs...
