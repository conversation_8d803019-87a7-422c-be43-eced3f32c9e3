Jun 15 03:33:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[3736]: finished logrotate
Jun 15 03:33:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[3696]: starting man-db.cron
Jun 15 03:33:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[3768]: finished man-db.cron
Jun 15 03:33:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[3696]: starting mlocate
Jun 15 03:33:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[3779]: finished mlocate
Jun 15 03:33:01 ip-10-106-1-152 anacron[31350]: Job `cron.daily' terminated
Jun 15 03:33:01 ip-10-106-1-152 anacron[31350]: Normal exit (1 job run)
Jun 15 03:40:01 ip-10-106-1-152 CROND[4807]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 03:50:01 ip-10-106-1-152 CROND[6290]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 03:50:01 ip-10-106-1-152 CROND[6291]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 04:00:01 ip-10-106-1-152 CROND[8237]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 04:00:01 ip-10-106-1-152 CROND[8238]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 04:00:01 ip-10-106-1-152 CROND[8239]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 04:01:01 ip-10-106-1-152 CROND[8401]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8401]: starting 0anacron
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8410]: finished 0anacron
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8401]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8421]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8401]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8428]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8401]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8435]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8401]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8443]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8401]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8452]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8401]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8459]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8401]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8467]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 04:10:02 ip-10-106-1-152 CROND[9725]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 04:10:02 ip-10-106-1-152 CROND[9726]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 04:20:01 ip-10-106-1-152 CROND[11844]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 04:30:01 ip-10-106-1-152 CROND[22549]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 04:30:01 ip-10-106-1-152 CROND[22551]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 04:30:01 ip-10-106-1-152 CROND[22552]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 04:30:01 ip-10-106-1-152 CROND[22553]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 04:40:01 ip-10-106-1-152 CROND[24062]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 04:50:01 ip-10-106-1-152 CROND[25495]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 04:50:01 ip-10-106-1-152 CROND[25501]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 05:00:01 ip-10-106-1-152 CROND[26876]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 05:00:01 ip-10-106-1-152 CROND[26877]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 05:00:01 ip-10-106-1-152 CROND[26878]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 05:01:01 ip-10-106-1-152 CROND[27035]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27035]: starting 0anacron
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27044]: finished 0anacron
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27035]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27055]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27035]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27062]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27035]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27069]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27035]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27077]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27035]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27086]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27035]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27093]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27035]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 05:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27102]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 05:10:01 ip-10-106-1-152 CROND[28403]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 05:10:01 ip-10-106-1-152 CROND[28409]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 05:20:01 ip-10-106-1-152 CROND[29816]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 05:30:01 ip-10-106-1-152 CROND[31236]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 05:30:01 ip-10-106-1-152 CROND[31242]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 05:30:01 ip-10-106-1-152 CROND[31235]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 05:30:01 ip-10-106-1-152 CROND[31243]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 05:40:01 ip-10-106-1-152 CROND[32702]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 05:50:01 ip-10-106-1-152 CROND[1660]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 05:50:01 ip-10-106-1-152 CROND[1666]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 06:00:01 ip-10-106-1-152 CROND[3770]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 06:00:01 ip-10-106-1-152 CROND[3772]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 06:00:01 ip-10-106-1-152 CROND[3771]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 06:01:01 ip-10-106-1-152 CROND[3930]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3930]: starting 0anacron
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3939]: finished 0anacron
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3930]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3950]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3930]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3957]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3930]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3964]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3930]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3972]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3930]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3981]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3930]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3988]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3930]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3996]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 06:10:01 ip-10-106-1-152 CROND[5257]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 06:10:01 ip-10-106-1-152 CROND[5256]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 06:20:01 ip-10-106-1-152 CROND[6672]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 06:30:02 ip-10-106-1-152 CROND[8098]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 06:30:02 ip-10-106-1-152 CROND[8099]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 06:30:02 ip-10-106-1-152 CROND[8101]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 06:30:02 ip-10-106-1-152 CROND[8100]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 06:40:01 ip-10-106-1-152 CROND[9514]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 06:50:01 ip-10-106-1-152 CROND[10968]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 06:50:01 ip-10-106-1-152 CROND[10969]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 07:00:01 ip-10-106-1-152 CROND[12348]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 07:00:01 ip-10-106-1-152 CROND[12347]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 07:00:01 ip-10-106-1-152 CROND[12353]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 07:01:01 ip-10-106-1-152 CROND[12506]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12506]: starting 0anacron
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12515]: finished 0anacron
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12506]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12526]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12506]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12533]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12506]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12540]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12506]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12548]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12506]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12557]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12506]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12564]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12506]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 07:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[12573]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 07:10:01 ip-10-106-1-152 CROND[13828]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 07:10:01 ip-10-106-1-152 CROND[13829]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 07:20:01 ip-10-106-1-152 CROND[15293]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 07:30:01 ip-10-106-1-152 CROND[16678]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 07:30:01 ip-10-106-1-152 CROND[16677]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 07:30:01 ip-10-106-1-152 CROND[16679]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 07:30:01 ip-10-106-1-152 CROND[16680]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 07:40:01 ip-10-106-1-152 CROND[18149]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 07:50:01 ip-10-106-1-152 CROND[19550]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 07:50:01 ip-10-106-1-152 CROND[19551]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 08:00:01 ip-10-106-1-152 CROND[21488]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 08:00:01 ip-10-106-1-152 CROND[21486]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 08:00:01 ip-10-106-1-152 CROND[21487]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 08:01:01 ip-10-106-1-152 CROND[21643]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21643]: starting 0anacron
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21652]: finished 0anacron
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21643]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21663]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21643]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21670]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21643]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21677]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21643]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21685]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21643]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21694]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21643]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21701]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21643]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 08:01:07 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21716]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 08:10:01 ip-10-106-1-152 CROND[23012]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 08:10:01 ip-10-106-1-152 CROND[23013]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 08:20:01 ip-10-106-1-152 CROND[24428]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 08:30:01 ip-10-106-1-152 CROND[3541]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 08:30:01 ip-10-106-1-152 CROND[3543]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 08:30:01 ip-10-106-1-152 CROND[3544]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 08:30:01 ip-10-106-1-152 CROND[3542]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 08:40:01 ip-10-106-1-152 CROND[5007]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 08:50:01 ip-10-106-1-152 CROND[6437]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 08:50:01 ip-10-106-1-152 CROND[6438]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 09:00:01 ip-10-106-1-152 CROND[7863]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 09:00:01 ip-10-106-1-152 CROND[7864]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 09:00:01 ip-10-106-1-152 CROND[7862]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 09:01:01 ip-10-106-1-152 CROND[8021]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8021]: starting 0anacron
Jun 15 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8030]: finished 0anacron
Jun 15 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8021]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8041]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8021]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8048]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8021]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8055]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8021]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8063]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8021]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8072]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8021]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8079]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8021]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8088]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 09:10:01 ip-10-106-1-152 CROND[9347]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 09:10:01 ip-10-106-1-152 CROND[9348]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 09:20:01 ip-10-106-1-152 CROND[10762]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 09:30:01 ip-10-106-1-152 CROND[12184]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 09:30:01 ip-10-106-1-152 CROND[12185]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 09:30:01 ip-10-106-1-152 CROND[12186]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 09:30:01 ip-10-106-1-152 CROND[12192]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 09:40:02 ip-10-106-1-152 CROND[13606]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 09:50:01 ip-10-106-1-152 CROND[15055]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 09:50:01 ip-10-106-1-152 CROND[15059]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 10:00:01 ip-10-106-1-152 CROND[16992]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 10:00:01 ip-10-106-1-152 CROND[16991]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 10:00:01 ip-10-106-1-152 CROND[16993]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 10:01:01 ip-10-106-1-152 CROND[17148]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17148]: starting 0anacron
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17157]: finished 0anacron
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17148]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17168]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17148]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17175]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17148]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17182]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17148]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17190]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17148]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17199]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17148]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17206]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17148]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17214]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 10:10:01 ip-10-106-1-152 CROND[18477]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 10:10:01 ip-10-106-1-152 CROND[18482]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 10:20:01 ip-10-106-1-152 CROND[19943]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 10:30:01 ip-10-106-1-152 CROND[21310]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 10:30:01 ip-10-106-1-152 CROND[21313]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 10:30:01 ip-10-106-1-152 CROND[21311]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 10:30:01 ip-10-106-1-152 CROND[21312]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 10:40:01 ip-10-106-1-152 CROND[22778]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 10:50:01 ip-10-106-1-152 CROND[24229]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 10:50:01 ip-10-106-1-152 CROND[24228]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 11:00:01 ip-10-106-1-152 CROND[25600]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 11:00:01 ip-10-106-1-152 CROND[25602]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 11:00:01 ip-10-106-1-152 CROND[25601]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 11:01:01 ip-10-106-1-152 CROND[25758]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25758]: starting 0anacron
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25767]: finished 0anacron
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25758]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25778]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25758]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25785]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25758]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25792]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25758]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25800]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25758]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25809]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25758]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25816]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 11:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25758]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25824]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 11:10:01 ip-10-106-1-152 CROND[27137]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 11:10:01 ip-10-106-1-152 CROND[27138]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 11:20:01 ip-10-106-1-152 CROND[28553]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 11:30:01 ip-10-106-1-152 CROND[29921]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 11:30:01 ip-10-106-1-152 CROND[29920]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 11:30:01 ip-10-106-1-152 CROND[29922]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 11:30:01 ip-10-106-1-152 CROND[29923]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 11:40:01 ip-10-106-1-152 CROND[31381]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 11:50:01 ip-10-106-1-152 CROND[397]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 11:50:01 ip-10-106-1-152 CROND[398]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 12:00:01 ip-10-106-1-152 CROND[2443]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 12:00:01 ip-10-106-1-152 CROND[2444]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 12:00:01 ip-10-106-1-152 CROND[2442]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 12:01:01 ip-10-106-1-152 CROND[2609]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2609]: starting 0anacron
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2618]: finished 0anacron
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2609]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2629]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2609]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2636]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2609]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2643]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2609]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2651]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2609]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2660]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2609]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2667]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2609]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 12:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2675]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 12:10:01 ip-10-106-1-152 CROND[4005]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 12:10:01 ip-10-106-1-152 CROND[4006]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 12:20:01 ip-10-106-1-152 CROND[5469]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 12:30:01 ip-10-106-1-152 CROND[16835]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 12:30:01 ip-10-106-1-152 CROND[16834]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 12:30:01 ip-10-106-1-152 CROND[16837]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 12:30:01 ip-10-106-1-152 CROND[16836]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 12:40:01 ip-10-106-1-152 CROND[18269]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 12:50:01 ip-10-106-1-152 CROND[20537]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 12:50:01 ip-10-106-1-152 CROND[20536]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 13:00:01 ip-10-106-1-152 CROND[21935]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 13:00:01 ip-10-106-1-152 CROND[21934]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 13:00:01 ip-10-106-1-152 CROND[21936]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 13:01:01 ip-10-106-1-152 CROND[22099]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22099]: starting 0anacron
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22108]: finished 0anacron
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22099]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22119]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22099]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22126]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22099]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22133]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22099]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22141]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 13:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22099]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 13:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22150]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 13:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22099]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 13:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22157]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 13:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22099]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 13:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[22166]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 13:10:01 ip-10-106-1-152 CROND[23438]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 13:10:01 ip-10-106-1-152 CROND[23439]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 13:20:01 ip-10-106-1-152 CROND[24956]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 13:30:01 ip-10-106-1-152 CROND[26346]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 13:30:01 ip-10-106-1-152 CROND[26347]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 13:30:01 ip-10-106-1-152 CROND[26349]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 13:30:01 ip-10-106-1-152 CROND[26348]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 13:40:01 ip-10-106-1-152 CROND[27881]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 13:50:01 ip-10-106-1-152 CROND[29297]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 13:50:01 ip-10-106-1-152 CROND[29299]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 14:00:01 ip-10-106-1-152 CROND[31247]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 14:00:01 ip-10-106-1-152 CROND[31248]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 14:00:01 ip-10-106-1-152 CROND[31249]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 14:01:01 ip-10-106-1-152 CROND[31501]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31501]: starting 0anacron
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31510]: finished 0anacron
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31501]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31521]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31501]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31528]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31501]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31535]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31501]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31543]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31501]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31552]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31501]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31559]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31501]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31567]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 14:10:01 ip-10-106-1-152 CROND[424]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 14:10:01 ip-10-106-1-152 CROND[425]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 14:20:01 ip-10-106-1-152 CROND[1895]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 14:30:01 ip-10-106-1-152 CROND[3446]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 14:30:01 ip-10-106-1-152 CROND[3447]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 14:30:01 ip-10-106-1-152 CROND[3448]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 14:30:01 ip-10-106-1-152 CROND[3449]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 14:40:01 ip-10-106-1-152 CROND[4888]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 14:50:01 ip-10-106-1-152 CROND[6299]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 14:50:01 ip-10-106-1-152 CROND[6300]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 15:00:01 ip-10-106-1-152 CROND[7763]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 15:00:01 ip-10-106-1-152 CROND[7764]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 15:00:01 ip-10-106-1-152 CROND[7765]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 15:01:01 ip-10-106-1-152 CROND[7939]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7939]: starting 0anacron
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7948]: finished 0anacron
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7939]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7959]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7939]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7967]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7939]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7978]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7939]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7990]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7939]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8006]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7939]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8016]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7939]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 15:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[8026]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 15:10:01 ip-10-106-1-152 CROND[9302]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 15:10:01 ip-10-106-1-152 CROND[9303]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 15:20:01 ip-10-106-1-152 CROND[10732]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 15:30:01 ip-10-106-1-152 CROND[12181]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 15:30:01 ip-10-106-1-152 CROND[12182]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 15:30:01 ip-10-106-1-152 CROND[12183]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 15:30:01 ip-10-106-1-152 CROND[12184]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 15:40:01 ip-10-106-1-152 CROND[13624]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 15:50:01 ip-10-106-1-152 CROND[15107]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 15:50:01 ip-10-106-1-152 CROND[15113]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 16:00:01 ip-10-106-1-152 CROND[17058]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 16:00:01 ip-10-106-1-152 CROND[17060]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 16:00:01 ip-10-106-1-152 CROND[17059]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 16:01:01 ip-10-106-1-152 CROND[17226]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17226]: starting 0anacron
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17235]: finished 0anacron
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17226]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17246]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17226]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17256]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17226]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17266]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17226]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17278]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17226]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17296]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17226]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17305]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17226]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[17315]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 16:10:01 ip-10-106-1-152 CROND[18592]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 16:10:01 ip-10-106-1-152 CROND[18591]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 16:20:01 ip-10-106-1-152 CROND[20086]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 16:30:01 ip-10-106-1-152 CROND[31463]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 16:30:01 ip-10-106-1-152 CROND[31464]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 16:30:01 ip-10-106-1-152 CROND[31465]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 16:30:01 ip-10-106-1-152 CROND[31462]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 16:40:01 ip-10-106-1-152 CROND[522]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 16:50:01 ip-10-106-1-152 CROND[2066]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 16:50:01 ip-10-106-1-152 CROND[2067]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 17:00:01 ip-10-106-1-152 CROND[3556]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 17:00:01 ip-10-106-1-152 CROND[3557]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 17:00:01 ip-10-106-1-152 CROND[3558]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 17:01:01 ip-10-106-1-152 CROND[3721]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3721]: starting 0anacron
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3730]: finished 0anacron
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3721]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3741]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3721]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3748]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3721]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3761]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3721]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3775]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3721]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3790]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3721]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3799]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3721]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 17:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[3808]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 17:10:01 ip-10-106-1-152 CROND[5144]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 17:10:01 ip-10-106-1-152 CROND[5145]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 17:20:01 ip-10-106-1-152 CROND[6571]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 17:30:01 ip-10-106-1-152 CROND[7951]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 17:30:01 ip-10-106-1-152 CROND[7953]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 17:30:01 ip-10-106-1-152 CROND[7952]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 17:30:01 ip-10-106-1-152 CROND[7957]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 17:35:01 ip-10-106-1-152 crond[2346]: (*system*) RELOAD (/etc/cron.d/0rotate_app_logs)
Jun 15 17:40:01 ip-10-106-1-152 CROND[15448]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 17:50:01 ip-10-106-1-152 CROND[16939]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 17:50:01 ip-10-106-1-152 CROND[16938]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 18:00:01 ip-10-106-1-152 CROND[18910]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 18:00:01 ip-10-106-1-152 CROND[18908]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 18:00:01 ip-10-106-1-152 CROND[18909]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 18:01:01 ip-10-106-1-152 CROND[19094]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19094]: starting 0anacron
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19109]: finished 0anacron
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19094]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19126]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19094]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19138]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19094]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19148]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19094]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19162]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19094]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19179]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19094]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19188]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19094]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 18:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[19204]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 18:10:01 ip-10-106-1-152 CROND[20603]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 18:10:01 ip-10-106-1-152 CROND[20604]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 18:20:01 ip-10-106-1-152 CROND[22020]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 18:30:01 ip-10-106-1-152 CROND[23410]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 18:30:01 ip-10-106-1-152 CROND[23411]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 18:30:01 ip-10-106-1-152 CROND[23412]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 18:30:01 ip-10-106-1-152 CROND[23413]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 18:40:01 ip-10-106-1-152 CROND[24970]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 18:50:01 ip-10-106-1-152 CROND[26357]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 18:50:01 ip-10-106-1-152 CROND[26356]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 19:00:01 ip-10-106-1-152 CROND[27749]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 19:00:01 ip-10-106-1-152 CROND[27750]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 19:00:01 ip-10-106-1-152 CROND[27751]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 19:01:01 ip-10-106-1-152 CROND[27993]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27993]: starting 0anacron
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28002]: finished 0anacron
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27993]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28013]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27993]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28021]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27993]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28033]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27993]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28045]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27993]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28064]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27993]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28073]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[27993]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28089]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 19:10:01 ip-10-106-1-152 CROND[29391]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 19:10:01 ip-10-106-1-152 CROND[29392]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 19:20:01 ip-10-106-1-152 CROND[30798]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 19:30:01 ip-10-106-1-152 CROND[458]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 19:30:01 ip-10-106-1-152 CROND[461]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 19:30:01 ip-10-106-1-152 CROND[462]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 19:30:01 ip-10-106-1-152 CROND[460]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 19:40:01 ip-10-106-1-152 CROND[2010]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 19:50:01 ip-10-106-1-152 CROND[3418]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 19:50:01 ip-10-106-1-152 CROND[3420]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 20:00:01 ip-10-106-1-152 CROND[5464]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 20:00:01 ip-10-106-1-152 CROND[5465]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 20:00:01 ip-10-106-1-152 CROND[5466]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 20:01:01 ip-10-106-1-152 CROND[5634]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5634]: starting 0anacron
Jun 15 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5643]: finished 0anacron
Jun 15 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5634]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5654]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5634]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5662]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5634]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5674]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5634]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5687]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5634]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5703]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5634]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5712]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5634]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5726]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 20:10:01 ip-10-106-1-152 CROND[7030]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 20:10:01 ip-10-106-1-152 CROND[7031]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 20:20:01 ip-10-106-1-152 CROND[8490]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 20:30:01 ip-10-106-1-152 CROND[19996]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 20:30:02 ip-10-106-1-152 CROND[19997]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 20:30:02 ip-10-106-1-152 CROND[19998]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 20:30:02 ip-10-106-1-152 CROND[20000]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 20:40:01 ip-10-106-1-152 CROND[21483]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 20:50:01 ip-10-106-1-152 CROND[22925]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 20:50:01 ip-10-106-1-152 CROND[22936]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 21:00:01 ip-10-106-1-152 CROND[24313]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 21:00:01 ip-10-106-1-152 CROND[24314]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 21:00:01 ip-10-106-1-152 CROND[24315]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 21:01:01 ip-10-106-1-152 CROND[24481]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24481]: starting 0anacron
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24490]: finished 0anacron
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24481]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24501]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24481]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24511]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24481]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24521]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24481]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24534]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24481]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24551]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24481]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24560]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24481]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24571]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 21:10:01 ip-10-106-1-152 CROND[25906]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 21:10:01 ip-10-106-1-152 CROND[25911]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 21:20:01 ip-10-106-1-152 CROND[27316]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 21:30:01 ip-10-106-1-152 CROND[28776]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 21:30:01 ip-10-106-1-152 CROND[28777]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 21:30:01 ip-10-106-1-152 CROND[28779]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 21:30:01 ip-10-106-1-152 CROND[28778]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 21:40:01 ip-10-106-1-152 CROND[30213]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 21:50:01 ip-10-106-1-152 CROND[31609]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 21:50:01 ip-10-106-1-152 CROND[31613]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 22:00:01 ip-10-106-1-152 CROND[1158]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 22:00:01 ip-10-106-1-152 CROND[1159]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 22:00:01 ip-10-106-1-152 CROND[1162]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 22:01:01 ip-10-106-1-152 CROND[1329]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1329]: starting 0anacron
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1338]: finished 0anacron
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1329]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1349]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1329]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1359]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1329]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1368]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1329]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1382]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1329]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1398]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1329]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1409]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1329]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 22:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1423]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 22:10:01 ip-10-106-1-152 CROND[2743]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 22:10:01 ip-10-106-1-152 CROND[2744]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 22:20:01 ip-10-106-1-152 CROND[4180]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 22:30:01 ip-10-106-1-152 CROND[5603]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 22:30:01 ip-10-106-1-152 CROND[5604]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 22:30:01 ip-10-106-1-152 CROND[5605]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 22:30:01 ip-10-106-1-152 CROND[5608]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 22:40:01 ip-10-106-1-152 CROND[7088]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 22:50:01 ip-10-106-1-152 CROND[8489]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 22:50:01 ip-10-106-1-152 CROND[8488]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 23:00:01 ip-10-106-1-152 CROND[9942]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 23:00:01 ip-10-106-1-152 CROND[9943]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 23:00:01 ip-10-106-1-152 CROND[9944]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 23:01:01 ip-10-106-1-152 CROND[10118]: (root) CMD (run-parts /etc/cron.hourly)
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10118]: starting 0anacron
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10127]: finished 0anacron
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10118]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10138]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10118]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10148]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10118]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10159]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10118]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10172]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10118]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10184]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10118]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10193]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 15 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10118]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 23:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10208]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 15 23:10:01 ip-10-106-1-152 CROND[11486]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 23:10:01 ip-10-106-1-152 CROND[11485]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 23:20:01 ip-10-106-1-152 CROND[12953]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 23:30:01 ip-10-106-1-152 CROND[14400]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 23:30:01 ip-10-106-1-152 CROND[14401]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 23:30:01 ip-10-106-1-152 CROND[14405]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 15 23:30:01 ip-10-106-1-152 CROND[14406]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 23:31:13 ip-10-106-1-152 crontab[19905]: (root) LIST (root)
Jun 15 23:40:01 ip-10-106-1-152 CROND[26405]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 23:50:01 ip-10-106-1-152 CROND[27850]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 15 23:50:01 ip-10-106-1-152 CROND[27851]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 15 23:53:02 ip-10-106-1-152 CROND[28281]: (root) CMD (/usr/lib64/sa/sa2 -A)
Jun 16 00:00:01 ip-10-106-1-152 CROND[29823]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 00:00:01 ip-10-106-1-152 CROND[29822]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 00:00:01 ip-10-106-1-152 CROND[29824]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 00:01:01 ip-10-106-1-152 CROND[29990]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29990]: starting 0anacron
Jun 16 00:01:01 ip-10-106-1-152 anacron[30000]: Anacron started on 2025-06-16
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30004]: finished 0anacron
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29990]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 00:01:01 ip-10-106-1-152 anacron[30000]: Normal exit (0 jobs run)
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30016]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29990]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30024]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29990]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30034]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29990]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30047]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29990]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30059]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29990]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30066]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29990]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 00:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30082]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 00:10:01 ip-10-106-1-152 CROND[31397]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 00:10:01 ip-10-106-1-152 CROND[31403]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 00:20:01 ip-10-106-1-152 CROND[345]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 00:30:01 ip-10-106-1-152 CROND[11837]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 00:30:01 ip-10-106-1-152 CROND[11836]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 00:30:01 ip-10-106-1-152 CROND[11835]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 00:30:01 ip-10-106-1-152 CROND[11838]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 00:40:01 ip-10-106-1-152 CROND[13390]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 00:50:01 ip-10-106-1-152 CROND[14818]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 00:50:01 ip-10-106-1-152 CROND[14819]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 00:58:01 ip-10-106-1-152 CROND[15929]: (root) CMD (/usr/bin/systemctl --quiet restart update-motd)
Jun 16 01:00:01 ip-10-106-1-152 CROND[16340]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 01:00:01 ip-10-106-1-152 CROND[16338]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 01:00:01 ip-10-106-1-152 CROND[16339]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 01:01:01 ip-10-106-1-152 CROND[16500]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16500]: starting 0anacron
Jun 16 01:01:01 ip-10-106-1-152 anacron[16510]: Anacron started on 2025-06-16
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16512]: finished 0anacron
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16500]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 01:01:01 ip-10-106-1-152 anacron[16510]: Normal exit (0 jobs run)
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16529]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16500]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16537]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16500]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16548]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16500]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16561]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16500]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16574]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16500]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16584]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16500]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 01:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16598]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 01:10:01 ip-10-106-1-152 CROND[17872]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 01:10:01 ip-10-106-1-152 CROND[17871]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 01:20:01 ip-10-106-1-152 CROND[19279]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 01:30:01 ip-10-106-1-152 CROND[20699]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 01:30:01 ip-10-106-1-152 CROND[20702]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 01:30:01 ip-10-106-1-152 CROND[20700]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 01:30:01 ip-10-106-1-152 CROND[20703]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 01:40:01 ip-10-106-1-152 CROND[22123]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 01:50:01 ip-10-106-1-152 CROND[23518]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 01:50:01 ip-10-106-1-152 CROND[23517]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 02:00:01 ip-10-106-1-152 CROND[25515]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 02:00:01 ip-10-106-1-152 CROND[25513]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 02:00:01 ip-10-106-1-152 CROND[25514]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 02:01:01 ip-10-106-1-152 CROND[25676]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25676]: starting 0anacron
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25689]: finished 0anacron
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25676]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 02:01:01 ip-10-106-1-152 anacron[25686]: Anacron started on 2025-06-16
Jun 16 02:01:01 ip-10-106-1-152 anacron[25686]: Normal exit (0 jobs run)
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25706]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25676]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25715]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25676]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25724]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25676]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25738]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25676]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25749]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25676]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25756]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25676]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25764]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 02:10:01 ip-10-106-1-152 CROND[27038]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 02:10:01 ip-10-106-1-152 CROND[27039]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 02:20:01 ip-10-106-1-152 CROND[28454]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 02:30:01 ip-10-106-1-152 CROND[29906]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 02:30:01 ip-10-106-1-152 CROND[29909]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 02:30:01 ip-10-106-1-152 CROND[29908]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 02:30:01 ip-10-106-1-152 CROND[29907]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 02:40:01 ip-10-106-1-152 CROND[31332]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 02:50:01 ip-10-106-1-152 CROND[315]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 02:50:01 ip-10-106-1-152 CROND[316]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 03:00:01 ip-10-106-1-152 CROND[1719]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 03:00:01 ip-10-106-1-152 CROND[1720]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 03:00:01 ip-10-106-1-152 CROND[1721]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 03:01:01 ip-10-106-1-152 CROND[1898]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1898]: starting 0anacron
Jun 16 03:01:01 ip-10-106-1-152 anacron[1908]: Anacron started on 2025-06-16
Jun 16 03:01:01 ip-10-106-1-152 anacron[1908]: Will run job `cron.daily' in 36 min.
Jun 16 03:01:01 ip-10-106-1-152 anacron[1908]: Jobs will be executed sequentially
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1913]: finished 0anacron
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1898]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1924]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1898]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1934]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1898]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1944]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1898]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1956]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1898]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1970]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1898]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1979]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1898]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 03:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1991]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 03:10:01 ip-10-106-1-152 CROND[3281]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 03:10:01 ip-10-106-1-152 CROND[3287]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 03:20:01 ip-10-106-1-152 CROND[4744]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 03:30:01 ip-10-106-1-152 CROND[6116]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 03:30:01 ip-10-106-1-152 CROND[6117]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 03:30:01 ip-10-106-1-152 CROND[6118]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 03:30:01 ip-10-106-1-152 CROND[6119]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 03:37:01 ip-10-106-1-152 anacron[1908]: Job `cron.daily' started
Jun 16 03:37:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[7127]: starting logrotate
Jun 16 03:37:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[7159]: finished logrotate
Jun 16 03:37:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[7127]: starting man-db.cron
Jun 16 03:37:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[7185]: finished man-db.cron
Jun 16 03:37:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[7127]: starting mlocate
Jun 16 03:37:02 ip-10-106-1-152 run-parts(/etc/cron.daily)[7221]: finished mlocate
Jun 16 03:37:02 ip-10-106-1-152 anacron[1908]: Job `cron.daily' terminated
Jun 16 03:37:02 ip-10-106-1-152 anacron[1908]: Normal exit (1 job run)
Jun 16 03:40:01 ip-10-106-1-152 CROND[7728]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 03:50:01 ip-10-106-1-152 CROND[9139]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 03:50:01 ip-10-106-1-152 CROND[9140]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 04:00:01 ip-10-106-1-152 CROND[11157]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 04:00:01 ip-10-106-1-152 CROND[11156]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 04:00:01 ip-10-106-1-152 CROND[11158]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 04:01:01 ip-10-106-1-152 CROND[11317]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11317]: starting 0anacron
Jun 16 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11327]: finished 0anacron
Jun 16 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11317]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11338]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11317]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11345]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11317]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11352]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11317]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11360]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11317]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11369]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11317]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11376]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11317]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11384]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 04:10:01 ip-10-106-1-152 CROND[12691]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 04:10:01 ip-10-106-1-152 CROND[12692]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 04:20:01 ip-10-106-1-152 CROND[14180]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 04:30:01 ip-10-106-1-152 CROND[22315]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 04:30:01 ip-10-106-1-152 CROND[22314]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 04:30:01 ip-10-106-1-152 CROND[22316]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 04:30:01 ip-10-106-1-152 CROND[22321]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 04:40:01 ip-10-106-1-152 CROND[27097]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 04:50:01 ip-10-106-1-152 CROND[28507]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 04:50:01 ip-10-106-1-152 CROND[28506]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 05:00:01 ip-10-106-1-152 CROND[29932]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 05:00:01 ip-10-106-1-152 CROND[29933]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 05:00:01 ip-10-106-1-152 CROND[29934]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 05:01:01 ip-10-106-1-152 CROND[30094]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30094]: starting 0anacron
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30103]: finished 0anacron
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30094]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30114]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30094]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30121]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30094]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30128]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30094]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30136]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30094]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30145]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30094]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30152]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30094]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 05:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[30163]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 05:10:01 ip-10-106-1-152 CROND[31424]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 05:10:01 ip-10-106-1-152 CROND[31430]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 05:20:01 ip-10-106-1-152 CROND[365]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 05:30:01 ip-10-106-1-152 CROND[1816]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 05:30:01 ip-10-106-1-152 CROND[1814]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 05:30:01 ip-10-106-1-152 CROND[1817]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 05:30:01 ip-10-106-1-152 CROND[1815]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 05:40:01 ip-10-106-1-152 CROND[3266]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 05:50:01 ip-10-106-1-152 CROND[4717]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 05:50:01 ip-10-106-1-152 CROND[4718]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 06:00:01 ip-10-106-1-152 CROND[6665]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 06:00:01 ip-10-106-1-152 CROND[6667]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 06:00:01 ip-10-106-1-152 CROND[6666]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 06:01:01 ip-10-106-1-152 CROND[6825]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6825]: starting 0anacron
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6834]: finished 0anacron
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6825]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6845]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6825]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6852]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6825]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6859]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6825]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6867]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6825]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 06:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6876]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6825]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6883]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6825]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 06:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6892]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 06:10:01 ip-10-106-1-152 CROND[8147]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 06:10:01 ip-10-106-1-152 CROND[8146]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 06:20:02 ip-10-106-1-152 CROND[9634]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 06:30:01 ip-10-106-1-152 CROND[11029]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 06:30:01 ip-10-106-1-152 CROND[11030]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 06:30:01 ip-10-106-1-152 CROND[11031]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 06:30:01 ip-10-106-1-152 CROND[11032]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 06:40:01 ip-10-106-1-152 CROND[12499]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 06:50:01 ip-10-106-1-152 CROND[13969]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 06:50:01 ip-10-106-1-152 CROND[13968]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 07:00:01 ip-10-106-1-152 CROND[15350]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 07:00:01 ip-10-106-1-152 CROND[15348]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 07:00:01 ip-10-106-1-152 CROND[15349]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 07:01:01 ip-10-106-1-152 CROND[15510]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15510]: starting 0anacron
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15519]: finished 0anacron
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15510]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15530]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15510]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15537]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15510]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15544]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15510]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15552]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15510]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15561]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15510]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15568]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 07:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15510]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 07:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15576]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 07:10:01 ip-10-106-1-152 CROND[16889]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 07:10:01 ip-10-106-1-152 CROND[16888]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 07:20:01 ip-10-106-1-152 CROND[18312]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 07:30:01 ip-10-106-1-152 CROND[19688]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 07:30:01 ip-10-106-1-152 CROND[19689]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 07:30:01 ip-10-106-1-152 CROND[19690]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 07:30:01 ip-10-106-1-152 CROND[19695]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 07:40:01 ip-10-106-1-152 CROND[21160]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 07:50:01 ip-10-106-1-152 CROND[22557]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 07:50:01 ip-10-106-1-152 CROND[22558]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 08:00:01 ip-10-106-1-152 CROND[24551]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 08:00:01 ip-10-106-1-152 CROND[24550]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 08:00:01 ip-10-106-1-152 CROND[24552]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 08:01:01 ip-10-106-1-152 CROND[24710]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24710]: starting 0anacron
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24719]: finished 0anacron
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24710]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24730]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24710]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24737]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24710]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24744]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24710]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24752]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24710]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24761]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24710]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24768]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 08:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24710]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 08:01:03 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24777]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 08:10:01 ip-10-106-1-152 CROND[26035]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 08:10:01 ip-10-106-1-152 CROND[26036]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 08:20:01 ip-10-106-1-152 CROND[27501]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 08:30:01 ip-10-106-1-152 CROND[28877]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 08:30:01 ip-10-106-1-152 CROND[28878]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 08:30:01 ip-10-106-1-152 CROND[28876]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 08:30:01 ip-10-106-1-152 CROND[28879]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 08:40:01 ip-10-106-1-152 CROND[7929]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 08:50:01 ip-10-106-1-152 CROND[9391]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 08:50:01 ip-10-106-1-152 CROND[9390]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 09:00:01 ip-10-106-1-152 CROND[10804]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 09:00:01 ip-10-106-1-152 CROND[10806]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 09:00:01 ip-10-106-1-152 CROND[10805]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 09:01:01 ip-10-106-1-152 CROND[10966]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10966]: starting 0anacron
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10975]: finished 0anacron
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10966]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10986]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10966]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10993]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10966]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11000]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10966]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11008]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 09:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10966]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11017]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10966]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11024]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[10966]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 09:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11033]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 09:10:01 ip-10-106-1-152 CROND[12293]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 09:10:01 ip-10-106-1-152 CROND[12294]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 09:20:01 ip-10-106-1-152 CROND[13820]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 09:30:01 ip-10-106-1-152 CROND[15198]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 09:30:01 ip-10-106-1-152 CROND[15199]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 09:30:01 ip-10-106-1-152 CROND[15205]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 09:30:01 ip-10-106-1-152 CROND[15206]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 09:40:01 ip-10-106-1-152 CROND[16667]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 09:50:01 ip-10-106-1-152 CROND[18060]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 09:50:01 ip-10-106-1-152 CROND[18061]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 10:00:01 ip-10-106-1-152 CROND[20004]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 10:00:01 ip-10-106-1-152 CROND[20006]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 10:00:01 ip-10-106-1-152 CROND[20007]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 10:01:01 ip-10-106-1-152 CROND[20163]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20163]: starting 0anacron
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20172]: finished 0anacron
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20163]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20183]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20163]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20190]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20163]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20197]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20163]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20205]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20163]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20214]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20163]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20221]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 10:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20163]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 10:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[20229]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 10:10:01 ip-10-106-1-152 CROND[21539]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 10:10:01 ip-10-106-1-152 CROND[21540]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 10:20:01 ip-10-106-1-152 CROND[22949]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 10:30:01 ip-10-106-1-152 CROND[24346]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 10:30:01 ip-10-106-1-152 CROND[24347]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 10:30:01 ip-10-106-1-152 CROND[24349]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 10:30:01 ip-10-106-1-152 CROND[24348]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 10:40:01 ip-10-106-1-152 CROND[25876]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 10:50:01 ip-10-106-1-152 CROND[27277]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 10:50:01 ip-10-106-1-152 CROND[27278]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 11:00:01 ip-10-106-1-152 CROND[28707]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 11:00:01 ip-10-106-1-152 CROND[28709]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 11:00:01 ip-10-106-1-152 CROND[28708]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 11:01:01 ip-10-106-1-152 CROND[28869]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28869]: starting 0anacron
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28878]: finished 0anacron
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28869]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28889]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28869]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28896]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28869]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28904]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28869]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28912]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28869]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28921]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28869]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28928]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28869]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 11:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[28936]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 11:10:01 ip-10-106-1-152 CROND[30192]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 11:10:01 ip-10-106-1-152 CROND[30193]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 11:20:01 ip-10-106-1-152 CROND[31603]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 11:30:01 ip-10-106-1-152 CROND[565]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 11:30:01 ip-10-106-1-152 CROND[567]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 11:30:01 ip-10-106-1-152 CROND[566]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 11:30:01 ip-10-106-1-152 CROND[568]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 11:40:01 ip-10-106-1-152 CROND[2020]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 11:50:01 ip-10-106-1-152 CROND[3489]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 11:50:01 ip-10-106-1-152 CROND[3490]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 12:00:01 ip-10-106-1-152 CROND[5463]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 12:00:01 ip-10-106-1-152 CROND[5465]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 12:00:01 ip-10-106-1-152 CROND[5464]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 12:01:01 ip-10-106-1-152 CROND[5627]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5627]: starting 0anacron
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5636]: finished 0anacron
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5627]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5647]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5627]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5654]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5627]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5661]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5627]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5669]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5627]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5678]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5627]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5685]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 12:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5627]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 12:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[5695]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 12:10:01 ip-10-106-1-152 CROND[6949]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 12:10:01 ip-10-106-1-152 CROND[6951]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 12:20:01 ip-10-106-1-152 CROND[8409]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 12:30:01 ip-10-106-1-152 CROND[9808]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 12:30:01 ip-10-106-1-152 CROND[9810]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 12:30:01 ip-10-106-1-152 CROND[9817]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 12:30:01 ip-10-106-1-152 CROND[9816]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 12:40:01 ip-10-106-1-152 CROND[21358]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 12:50:01 ip-10-106-1-152 CROND[22806]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 12:50:01 ip-10-106-1-152 CROND[22812]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 13:00:01 ip-10-106-1-152 CROND[24187]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 13:00:01 ip-10-106-1-152 CROND[24188]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 13:00:01 ip-10-106-1-152 CROND[24189]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 13:01:01 ip-10-106-1-152 CROND[24349]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24349]: starting 0anacron
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24358]: finished 0anacron
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24349]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24369]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24349]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24376]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24349]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24383]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24349]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24391]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24349]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24400]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24349]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24407]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 13:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24349]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 13:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[24416]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 13:10:01 ip-10-106-1-152 CROND[25726]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 13:10:01 ip-10-106-1-152 CROND[25725]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 13:20:01 ip-10-106-1-152 CROND[27132]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 13:30:01 ip-10-106-1-152 CROND[29193]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 13:30:01 ip-10-106-1-152 CROND[29194]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 13:30:01 ip-10-106-1-152 CROND[29195]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 13:30:01 ip-10-106-1-152 CROND[29196]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 13:40:01 ip-10-106-1-152 CROND[30689]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 13:50:01 ip-10-106-1-152 CROND[32083]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 13:50:01 ip-10-106-1-152 CROND[32082]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 14:00:01 ip-10-106-1-152 CROND[1579]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 14:00:01 ip-10-106-1-152 CROND[1580]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 14:00:01 ip-10-106-1-152 CROND[1582]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 14:01:01 ip-10-106-1-152 CROND[1740]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1740]: starting 0anacron
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1749]: finished 0anacron
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1740]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1760]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1740]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1767]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1740]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1776]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1740]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 14:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1784]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 14:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1740]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 14:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1793]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 14:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1740]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 14:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1800]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 14:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1740]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 14:01:03 ip-10-106-1-152 run-parts(/etc/cron.hourly)[1816]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 14:07:01 ip-10-106-1-152 crond[2346]: (*system*) RELOAD (/etc/cron.d/0rotate_app_logs)
Jun 16 14:10:01 ip-10-106-1-152 CROND[7994]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 14:10:01 ip-10-106-1-152 CROND[7995]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 14:20:01 ip-10-106-1-152 CROND[9915]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 14:30:01 ip-10-106-1-152 CROND[11318]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 14:30:01 ip-10-106-1-152 CROND[11319]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 14:30:01 ip-10-106-1-152 CROND[11317]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 14:30:01 ip-10-106-1-152 CROND[11320]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 14:40:01 ip-10-106-1-152 CROND[12877]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 14:50:01 ip-10-106-1-152 CROND[14301]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 14:50:01 ip-10-106-1-152 CROND[14308]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 15:00:01 ip-10-106-1-152 CROND[15794]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 15:00:01 ip-10-106-1-152 CROND[15795]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 15:00:01 ip-10-106-1-152 CROND[15793]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 15:01:01 ip-10-106-1-152 CROND[15967]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15967]: starting 0anacron
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15984]: finished 0anacron
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15967]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15999]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15967]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16012]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15967]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16023]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15967]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16035]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15967]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16051]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15967]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16060]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 15:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[15967]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 15:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16075]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 15:10:01 ip-10-106-1-152 CROND[17363]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 15:10:01 ip-10-106-1-152 CROND[17364]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 15:20:01 ip-10-106-1-152 CROND[18792]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 15:30:01 ip-10-106-1-152 CROND[20258]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 15:30:01 ip-10-106-1-152 CROND[20260]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 15:30:01 ip-10-106-1-152 CROND[20261]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 15:30:01 ip-10-106-1-152 CROND[20259]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 15:40:01 ip-10-106-1-152 CROND[21706]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 15:50:01 ip-10-106-1-152 CROND[23191]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 15:50:01 ip-10-106-1-152 CROND[23192]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 16:00:01 ip-10-106-1-152 CROND[25154]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 16:00:01 ip-10-106-1-152 CROND[25155]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 16:00:01 ip-10-106-1-152 CROND[25156]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 16:01:01 ip-10-106-1-152 CROND[25319]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25319]: starting 0anacron
Jun 16 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25328]: finished 0anacron
Jun 16 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25319]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25339]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25319]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25349]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 16:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25319]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 16:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25359]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 16:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25319]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 16:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25372]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 16:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25319]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 16:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25387]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 16:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25319]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 16:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25397]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 16:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25319]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 16:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25411]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 16:10:01 ip-10-106-1-152 CROND[26698]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 16:10:01 ip-10-106-1-152 CROND[26699]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 16:20:02 ip-10-106-1-152 CROND[28186]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 16:30:01 ip-10-106-1-152 CROND[29564]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 16:30:01 ip-10-106-1-152 CROND[29567]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 16:30:01 ip-10-106-1-152 CROND[29568]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 16:30:01 ip-10-106-1-152 CROND[29565]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 16:40:01 ip-10-106-1-152 CROND[8787]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 16:50:01 ip-10-106-1-152 CROND[10247]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 16:50:01 ip-10-106-1-152 CROND[10250]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 17:00:01 ip-10-106-1-152 CROND[11655]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 17:00:01 ip-10-106-1-152 CROND[11656]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 17:00:01 ip-10-106-1-152 CROND[11657]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 17:01:01 ip-10-106-1-152 CROND[11816]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11816]: starting 0anacron
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11825]: finished 0anacron
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11816]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11836]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11816]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11846]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11816]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11857]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11816]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11869]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11816]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11887]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11816]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11898]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 17:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11816]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 17:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11912]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 17:10:01 ip-10-106-1-152 CROND[13285]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 17:10:01 ip-10-106-1-152 CROND[13284]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 17:20:01 ip-10-106-1-152 CROND[14680]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 17:30:01 ip-10-106-1-152 CROND[16084]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 17:30:01 ip-10-106-1-152 CROND[16087]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 17:30:01 ip-10-106-1-152 CROND[16083]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 17:30:01 ip-10-106-1-152 CROND[16085]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 17:40:01 ip-10-106-1-152 CROND[17579]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 17:50:01 ip-10-106-1-152 CROND[18947]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 17:50:01 ip-10-106-1-152 CROND[18948]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 18:00:01 ip-10-106-1-152 CROND[21016]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 18:00:01 ip-10-106-1-152 CROND[21018]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 18:00:01 ip-10-106-1-152 CROND[21020]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 18:01:01 ip-10-106-1-152 CROND[21176]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21176]: starting 0anacron
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21185]: finished 0anacron
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21176]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21196]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21176]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21205]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21176]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21216]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21176]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21229]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21176]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21246]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21176]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21256]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 18:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21176]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 18:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[21269]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 18:10:01 ip-10-106-1-152 CROND[22541]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 18:10:01 ip-10-106-1-152 CROND[22542]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 18:20:01 ip-10-106-1-152 CROND[23931]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 18:30:01 ip-10-106-1-152 CROND[25406]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 18:30:01 ip-10-106-1-152 CROND[25407]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 18:30:01 ip-10-106-1-152 CROND[25412]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 18:30:01 ip-10-106-1-152 CROND[25415]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 18:40:01 ip-10-106-1-152 CROND[26831]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 18:50:01 ip-10-106-1-152 CROND[28248]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 18:50:01 ip-10-106-1-152 CROND[28254]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 19:00:01 ip-10-106-1-152 CROND[29660]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 19:00:01 ip-10-106-1-152 CROND[29661]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 19:00:01 ip-10-106-1-152 CROND[29659]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 19:01:01 ip-10-106-1-152 CROND[29820]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29820]: starting 0anacron
Jun 16 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29829]: finished 0anacron
Jun 16 19:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29820]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29840]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29820]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29848]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29820]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29859]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29820]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29873]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29820]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29889]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29820]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29900]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29820]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 19:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[29911]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 19:10:01 ip-10-106-1-152 CROND[31187]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 19:10:01 ip-10-106-1-152 CROND[31193]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 19:20:01 ip-10-106-1-152 CROND[32623]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 19:30:01 ip-10-106-1-152 CROND[1582]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 19:30:01 ip-10-106-1-152 CROND[1583]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 19:30:01 ip-10-106-1-152 CROND[1584]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 19:30:01 ip-10-106-1-152 CROND[1585]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 19:40:01 ip-10-106-1-152 CROND[3055]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 19:50:01 ip-10-106-1-152 CROND[4524]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 19:50:01 ip-10-106-1-152 CROND[4525]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 20:00:01 ip-10-106-1-152 CROND[6536]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 20:00:01 ip-10-106-1-152 CROND[6537]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 20:00:01 ip-10-106-1-152 CROND[6538]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 20:01:01 ip-10-106-1-152 CROND[6732]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6732]: starting 0anacron
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6741]: finished 0anacron
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6732]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6756]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6732]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6767]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6732]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6779]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6732]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6791]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6732]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6802]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6732]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6814]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 20:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6732]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 20:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[6826]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 20:10:01 ip-10-106-1-152 CROND[8129]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 20:10:01 ip-10-106-1-152 CROND[8130]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 20:20:01 ip-10-106-1-152 CROND[9566]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 20:30:01 ip-10-106-1-152 CROND[10969]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 20:30:01 ip-10-106-1-152 CROND[10970]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 20:30:01 ip-10-106-1-152 CROND[10968]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 20:30:01 ip-10-106-1-152 CROND[10967]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 20:40:01 ip-10-106-1-152 CROND[18764]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 20:50:01 ip-10-106-1-152 CROND[23806]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 20:50:01 ip-10-106-1-152 CROND[23805]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 21:00:01 ip-10-106-1-152 CROND[25210]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 21:00:01 ip-10-106-1-152 CROND[25211]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 21:00:01 ip-10-106-1-152 CROND[25212]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 21:01:01 ip-10-106-1-152 CROND[25367]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25367]: starting 0anacron
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25376]: finished 0anacron
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25367]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25387]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25367]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25397]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25367]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25407]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25367]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25419]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25367]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25431]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25367]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25442]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 21:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25367]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 21:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25455]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 21:10:01 ip-10-106-1-152 CROND[26805]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 21:10:01 ip-10-106-1-152 CROND[26806]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 21:20:01 ip-10-106-1-152 CROND[28191]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 21:30:01 ip-10-106-1-152 CROND[29650]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 21:30:01 ip-10-106-1-152 CROND[29651]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 21:30:01 ip-10-106-1-152 CROND[29652]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 21:30:01 ip-10-106-1-152 CROND[29653]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 21:40:01 ip-10-106-1-152 CROND[31073]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 21:50:01 ip-10-106-1-152 CROND[32438]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 21:50:01 ip-10-106-1-152 CROND[32439]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 22:00:01 ip-10-106-1-152 CROND[2047]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 22:00:01 ip-10-106-1-152 CROND[2046]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 22:00:01 ip-10-106-1-152 CROND[2048]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 22:01:01 ip-10-106-1-152 CROND[2205]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2205]: starting 0anacron
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2214]: finished 0anacron
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2205]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2225]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2205]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2235]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2205]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2246]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2205]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2260]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2205]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2272]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2205]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2282]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 22:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2205]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 22:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[2296]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 22:10:01 ip-10-106-1-152 CROND[3621]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 22:10:01 ip-10-106-1-152 CROND[3622]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 22:20:01 ip-10-106-1-152 CROND[5026]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 22:30:01 ip-10-106-1-152 CROND[6482]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 22:30:01 ip-10-106-1-152 CROND[6486]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 22:30:01 ip-10-106-1-152 CROND[6485]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 22:30:01 ip-10-106-1-152 CROND[6493]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 22:40:01 ip-10-106-1-152 CROND[8038]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 22:50:01 ip-10-106-1-152 CROND[9455]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 22:50:01 ip-10-106-1-152 CROND[9454]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 23:00:01 ip-10-106-1-152 CROND[10862]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 23:00:01 ip-10-106-1-152 CROND[10864]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 23:00:01 ip-10-106-1-152 CROND[10863]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 23:01:01 ip-10-106-1-152 CROND[11018]: (root) CMD (run-parts /etc/cron.hourly)
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11018]: starting 0anacron
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11027]: finished 0anacron
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11018]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11038]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11018]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11046]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11018]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11056]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11018]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11070]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11018]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11082]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11018]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11093]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 16 23:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11018]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 23:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[11107]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 16 23:10:01 ip-10-106-1-152 CROND[13196]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 23:10:01 ip-10-106-1-152 CROND[13198]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 23:20:01 ip-10-106-1-152 CROND[14581]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 23:30:01 ip-10-106-1-152 CROND[16008]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 23:30:01 ip-10-106-1-152 CROND[16009]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 23:30:01 ip-10-106-1-152 CROND[16010]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 23:30:01 ip-10-106-1-152 CROND[16011]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 16 23:40:01 ip-10-106-1-152 CROND[27583]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 23:50:01 ip-10-106-1-152 CROND[28951]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 16 23:50:01 ip-10-106-1-152 CROND[28950]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 16 23:53:01 ip-10-106-1-152 CROND[29372]: (root) CMD (/usr/lib64/sa/sa2 -A)
Jun 17 00:00:01 ip-10-106-1-152 CROND[30911]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 00:00:01 ip-10-106-1-152 CROND[30910]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 00:00:01 ip-10-106-1-152 CROND[30912]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 00:01:01 ip-10-106-1-152 CROND[31068]: (root) CMD (run-parts /etc/cron.hourly)
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31068]: starting 0anacron
Jun 17 00:01:01 ip-10-106-1-152 anacron[31078]: Anacron started on 2025-06-17
Jun 17 00:01:01 ip-10-106-1-152 anacron[31078]: Normal exit (0 jobs run)
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31084]: finished 0anacron
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31068]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31097]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31068]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31105]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31068]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31115]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31068]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31128]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31068]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31140]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31068]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31150]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 00:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31068]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 00:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31166]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 00:10:01 ip-10-106-1-152 CROND[32517]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 00:10:01 ip-10-106-1-152 CROND[32518]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 00:20:01 ip-10-106-1-152 CROND[1457]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 00:30:01 ip-10-106-1-152 CROND[2958]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 00:30:01 ip-10-106-1-152 CROND[2959]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 00:30:01 ip-10-106-1-152 CROND[2957]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 00:30:01 ip-10-106-1-152 CROND[2961]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 00:40:01 ip-10-106-1-152 CROND[4440]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 00:50:01 ip-10-106-1-152 CROND[5811]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 00:50:01 ip-10-106-1-152 CROND[5812]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 00:58:01 ip-10-106-1-152 CROND[7035]: (root) CMD (/usr/bin/systemctl --quiet restart update-motd)
Jun 17 01:00:01 ip-10-106-1-152 CROND[7428]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 01:00:01 ip-10-106-1-152 CROND[7426]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 01:00:01 ip-10-106-1-152 CROND[7427]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 01:01:01 ip-10-106-1-152 CROND[7579]: (root) CMD (run-parts /etc/cron.hourly)
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7579]: starting 0anacron
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7591]: finished 0anacron
Jun 17 01:01:01 ip-10-106-1-152 anacron[7589]: Anacron started on 2025-06-17
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7579]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 01:01:01 ip-10-106-1-152 anacron[7589]: Normal exit (0 jobs run)
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7609]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7579]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7616]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7579]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7623]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7579]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 01:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7631]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 01:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7579]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 01:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7640]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 01:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7579]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 01:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7647]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 01:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7579]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 01:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[7656]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 01:10:01 ip-10-106-1-152 CROND[8927]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 01:10:01 ip-10-106-1-152 CROND[8926]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 01:20:01 ip-10-106-1-152 CROND[10358]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 01:30:02 ip-10-106-1-152 CROND[11758]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 01:30:02 ip-10-106-1-152 CROND[11759]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 01:30:02 ip-10-106-1-152 CROND[11760]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 01:30:02 ip-10-106-1-152 CROND[11761]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 01:40:01 ip-10-106-1-152 CROND[13186]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 01:50:01 ip-10-106-1-152 CROND[14601]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 01:50:01 ip-10-106-1-152 CROND[14602]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 02:00:01 ip-10-106-1-152 CROND[16565]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 02:00:01 ip-10-106-1-152 CROND[16567]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 02:00:01 ip-10-106-1-152 CROND[16566]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 02:01:01 ip-10-106-1-152 CROND[16724]: (root) CMD (run-parts /etc/cron.hourly)
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16724]: starting 0anacron
Jun 17 02:01:01 ip-10-106-1-152 anacron[16734]: Anacron started on 2025-06-17
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16737]: finished 0anacron
Jun 17 02:01:01 ip-10-106-1-152 anacron[16734]: Will run job `cron.monthly' in 82 min.
Jun 17 02:01:01 ip-10-106-1-152 anacron[16734]: Jobs will be executed sequentially
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16724]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16749]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16724]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16756]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16724]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16763]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16724]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16771]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16724]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16780]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16724]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16787]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 02:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16724]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 02:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[16795]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 02:10:01 ip-10-106-1-152 CROND[18067]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 02:10:01 ip-10-106-1-152 CROND[18068]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 02:20:01 ip-10-106-1-152 CROND[19497]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 02:30:01 ip-10-106-1-152 CROND[20921]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 02:30:01 ip-10-106-1-152 CROND[20922]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 02:30:01 ip-10-106-1-152 CROND[20923]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 02:30:01 ip-10-106-1-152 CROND[20924]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 02:40:01 ip-10-106-1-152 CROND[22400]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 02:50:01 ip-10-106-1-152 CROND[23763]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 02:50:01 ip-10-106-1-152 CROND[23762]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 03:00:01 ip-10-106-1-152 CROND[25162]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 03:00:01 ip-10-106-1-152 CROND[25161]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 03:00:01 ip-10-106-1-152 CROND[25163]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 03:01:01 ip-10-106-1-152 CROND[25315]: (root) CMD (run-parts /etc/cron.hourly)
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25315]: starting 0anacron
Jun 17 03:01:01 ip-10-106-1-152 anacron[25325]: Anacron started on 2025-06-17
Jun 17 03:01:01 ip-10-106-1-152 anacron[25325]: Job `cron.monthly' locked by another anacron - skipping
Jun 17 03:01:01 ip-10-106-1-152 anacron[25325]: Will run job `cron.daily' in 44 min.
Jun 17 03:01:01 ip-10-106-1-152 anacron[25325]: Jobs will be executed sequentially
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25329]: finished 0anacron
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25315]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25340]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25315]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25347]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25315]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25354]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25315]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25362]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25315]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25371]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25315]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25378]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 03:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25315]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 03:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[25387]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 03:08:29 ip-10-106-1-152 crontab[31872]: (root) LIST (root)
Jun 17 03:10:01 ip-10-106-1-152 CROND[4986]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 03:10:01 ip-10-106-1-152 CROND[4987]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 03:20:01 ip-10-106-1-152 CROND[6364]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 03:23:01 ip-10-106-1-152 anacron[16734]: Job `cron.monthly' started
Jun 17 03:23:01 ip-10-106-1-152 anacron[16734]: Job `cron.monthly' terminated
Jun 17 03:23:01 ip-10-106-1-152 anacron[16734]: Normal exit (1 job run)
Jun 17 03:30:01 ip-10-106-1-152 CROND[7843]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 03:30:01 ip-10-106-1-152 CROND[7844]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 03:30:01 ip-10-106-1-152 CROND[7851]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 03:30:01 ip-10-106-1-152 CROND[7852]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 03:40:01 ip-10-106-1-152 CROND[14849]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 03:45:01 ip-10-106-1-152 anacron[25325]: Job `cron.daily' started
Jun 17 03:45:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[20037]: starting logrotate
Jun 17 03:45:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[20070]: finished logrotate
Jun 17 03:45:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[20037]: starting man-db.cron
Jun 17 03:45:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[20096]: finished man-db.cron
Jun 17 03:45:01 ip-10-106-1-152 run-parts(/etc/cron.daily)[20037]: starting mlocate
Jun 17 03:45:02 ip-10-106-1-152 run-parts(/etc/cron.daily)[20130]: finished mlocate
Jun 17 03:45:02 ip-10-106-1-152 anacron[25325]: Job `cron.daily' terminated
Jun 17 03:45:02 ip-10-106-1-152 anacron[25325]: Normal exit (1 job run)
Jun 17 03:50:01 ip-10-106-1-152 CROND[20867]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 03:50:01 ip-10-106-1-152 CROND[20873]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 04:00:01 ip-10-106-1-152 CROND[22848]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 04:00:01 ip-10-106-1-152 CROND[22847]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 04:00:01 ip-10-106-1-152 CROND[22850]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 04:01:01 ip-10-106-1-152 CROND[23002]: (root) CMD (run-parts /etc/cron.hourly)
Jun 17 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23002]: starting 0anacron
Jun 17 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23011]: finished 0anacron
Jun 17 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23002]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23022]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 04:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23002]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23029]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23002]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23036]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23002]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23044]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23002]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23053]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23002]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23060]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23002]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 04:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[23069]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 04:10:01 ip-10-106-1-152 CROND[24406]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 04:10:01 ip-10-106-1-152 CROND[24405]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 04:20:01 ip-10-106-1-152 CROND[25834]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 04:30:01 ip-10-106-1-152 CROND[27232]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 04:30:01 ip-10-106-1-152 CROND[27233]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 04:30:01 ip-10-106-1-152 CROND[27240]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 04:30:01 ip-10-106-1-152 CROND[27241]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 04:40:01 ip-10-106-1-152 CROND[28714]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 04:50:01 ip-10-106-1-152 CROND[30131]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 04:50:01 ip-10-106-1-152 CROND[30130]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 05:00:01 ip-10-106-1-152 CROND[31543]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 05:00:01 ip-10-106-1-152 CROND[31545]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 05:00:01 ip-10-106-1-152 CROND[31547]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 05:01:01 ip-10-106-1-152 CROND[31699]: (root) CMD (run-parts /etc/cron.hourly)
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31699]: starting 0anacron
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31708]: finished 0anacron
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31699]: starting cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31719]: finished cron.logcleanup.elasticbeanstalk.healthd-proxy.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31699]: starting cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31726]: finished cron.logrotate.elasticbeanstalk.eb-engine.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31699]: starting cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31733]: finished cron.logrotate.elasticbeanstalk.eb-hooks.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31699]: starting cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31741]: finished cron.logrotate.elasticbeanstalk.healthd.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31699]: starting cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31750]: finished cron.logrotate.elasticbeanstalk.nginx.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31699]: starting cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31757]: finished cron.logrotate.elasticbeanstalk.web-stderr.conf
Jun 17 05:01:01 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31699]: starting cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 05:01:02 ip-10-106-1-152 run-parts(/etc/cron.hourly)[31766]: finished cron.logrotate.elasticbeanstalk.web-stdout.conf
Jun 17 05:10:01 ip-10-106-1-152 CROND[622]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 05:10:01 ip-10-106-1-152 CROND[621]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 05:20:01 ip-10-106-1-152 CROND[2040]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 05:30:01 ip-10-106-1-152 CROND[3540]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 05:30:01 ip-10-106-1-152 CROND[3541]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 05:30:01 ip-10-106-1-152 CROND[3539]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
Jun 17 05:30:01 ip-10-106-1-152 CROND[3543]: (root) CMD (run-parts /etc/cron.rotate_app_logs)
Jun 17 05:40:01 ip-10-106-1-152 CROND[4992]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 05:50:01 ip-10-106-1-152 CROND[6437]: (root) CMD (/usr/lib64/sa/sa1 1 1)
Jun 17 05:50:01 ip-10-106-1-152 CROND[6438]: (root) CMD (/opt/elasticbeanstalk/bin/publishlogs -type publish)
