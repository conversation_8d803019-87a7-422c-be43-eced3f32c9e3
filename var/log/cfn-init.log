2025-03-19 02:11:07,028 [INFO] -----------------------Starting build-----------------------
2025-03-19 02:11:07,034 [INFO] Running configSets: _OnInstanceBoot
2025-03-19 02:11:07,036 [INFO] Running configSet _OnInstanceBoot
2025-03-19 02:11:07,040 [INFO] Running config AWSEBBaseConfig
2025-03-19 02:11:07,359 [INFO] Command clearbackupfiles succeeded
2025-03-19 02:11:07,362 [INFO] Running config AWSEBCfnHupEndpointOverride
2025-03-19 02:11:07,365 [INFO] Command clearbackupfiles succeeded
2025-03-19 02:11:07,366 [INFO] ConfigSets completed
2025-03-19 02:11:07,366 [INFO] -----------------------Build complete-----------------------
2025-03-19 02:11:10,608 [INFO] -----------------------Starting build-----------------------
2025-03-19 02:11:10,617 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-03-19 02:11:10,619 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-03-19 02:11:10,622 [INFO] Running config prebuild_0_ics_api_nodejs
2025-03-19 02:11:30,622 [INFO] Yum installed ['expect', 'rpm-build']
2025-03-19 02:11:30,625 [INFO] Command 00_remove_legacy_scripts succeeded
2025-03-19 02:11:31,665 [INFO] Command 01_install_dependencies succeeded
2025-03-19 02:11:32,633 [INFO] Command 01_install_hsm_client succeeded
2025-03-19 02:11:33,243 [INFO] Command 02_install_hsm_libraries succeeded
2025-03-19 02:11:36,037 [INFO] Command 03_get_hsm_certificate succeeded
2025-03-19 02:11:36,476 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-03-19 02:11:36,509 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-03-19 02:11:36,567 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-03-19 02:11:36,573 [INFO] Command 07_start_cloudhsm_client succeeded
2025-03-19 02:11:37,373 [INFO] Command 08_define_hsm_credentials succeeded
2025-03-19 02:11:37,914 [INFO] Command 09_download_cryptokitool succeeded
2025-03-19 02:11:37,918 [INFO] Running config prebuild_1_ics_api_nodejs
2025-03-19 02:11:37,923 [INFO] Running config prebuild_2_ics_api_nodejs
2025-03-19 02:11:37,926 [INFO] Running config prebuild_3_ics_api_nodejs
2025-03-19 02:11:38,270 [INFO] Running config prebuild_4_ics_api_nodejs
2025-03-19 02:11:38,321 [INFO] Command 01-create-yum-repo succeeded
2025-03-19 02:11:39,039 [INFO] Command 02-update-yum-cache succeeded
2025-03-19 02:11:46,154 [INFO] Command 03-install-newrelicinfra succeeded
2025-03-19 02:11:46,178 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-03-19 02:11:46,275 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-03-19 02:11:46,280 [INFO] Running config prebuild_5_ics_api_nodejs
2025-03-19 02:11:46,286 [INFO] Running config prebuild_6_ics_api_nodejs
2025-03-19 02:11:46,309 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-03-19 02:11:46,501 [INFO] Running config prebuild_7_ics_api_nodejs
2025-03-19 02:11:46,502 [INFO] ConfigSets completed
2025-03-19 02:11:46,502 [INFO] -----------------------Build complete-----------------------
2025-03-19 02:11:51,667 [INFO] -----------------------Starting build-----------------------
2025-03-19 02:11:51,672 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-03-19 02:11:51,674 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-03-19 02:11:51,676 [INFO] Running config postbuild_0_ics_api_nodejs
2025-03-19 02:12:06,231 [INFO] Command populate_creds succeeded
2025-03-19 02:12:06,234 [INFO] Running config postbuild_1_ics_api_nodejs
2025-03-19 02:12:06,673 [INFO] Command fetch_key succeeded
2025-03-19 02:12:06,691 [INFO] Command update_key succeeded
2025-03-19 02:12:06,694 [INFO] Running config postbuild_2_ics_api_nodejs
2025-03-19 02:12:07,775 [INFO] Command 01-install succeeded
2025-03-19 02:12:11,541 [INFO] Command 02-activate succeeded
2025-03-19 02:12:11,543 [INFO] ConfigSets completed
2025-03-19 02:12:11,543 [INFO] -----------------------Build complete-----------------------
2025-03-20 03:55:52,407 [INFO] -----------------------Starting build-----------------------
2025-03-20 03:55:52,419 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-03-20 03:55:52,425 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-03-20 03:55:52,431 [INFO] Running config prebuild_0_ics_api_nodejs
2025-03-20 03:55:55,812 [INFO] Command 00_remove_legacy_scripts succeeded
2025-03-20 03:55:59,004 [INFO] Command 01_install_dependencies succeeded
2025-03-20 03:55:59,866 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-20 03:55:59,866 [INFO] ignoreErrors set to true, continuing build
2025-03-20 03:56:00,640 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-20 03:56:00,640 [INFO] ignoreErrors set to true, continuing build
2025-03-20 03:56:01,406 [INFO] Command 03_get_hsm_certificate succeeded
2025-03-20 03:56:02,165 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-03-20 03:56:02,248 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-03-20 03:56:02,391 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-03-20 03:56:02,420 [INFO] Command 07_start_cloudhsm_client succeeded
2025-03-20 03:56:03,836 [INFO] Command 08_define_hsm_credentials succeeded
2025-03-20 03:56:04,669 [INFO] Command 09_download_cryptokitool succeeded
2025-03-20 03:56:04,674 [INFO] Running config prebuild_1_ics_api_nodejs
2025-03-20 03:56:04,693 [INFO] Running config prebuild_2_ics_api_nodejs
2025-03-20 03:56:04,701 [INFO] Running config prebuild_3_ics_api_nodejs
2025-03-20 03:56:05,070 [INFO] Running config prebuild_4_ics_api_nodejs
2025-03-20 03:56:05,135 [INFO] Command 01-create-yum-repo succeeded
2025-03-20 03:56:05,787 [INFO] Command 02-update-yum-cache succeeded
2025-03-20 03:56:08,632 [INFO] Command 03-install-newrelicinfra succeeded
2025-03-20 03:56:08,678 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-03-20 03:56:09,840 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-03-20 03:56:09,854 [INFO] Running config prebuild_5_ics_api_nodejs
2025-03-20 03:56:09,884 [INFO] Running config prebuild_6_ics_api_nodejs
2025-03-20 03:56:09,915 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-03-20 03:56:10,163 [INFO] Running config prebuild_7_ics_api_nodejs
2025-03-20 03:56:10,171 [INFO] ConfigSets completed
2025-03-20 03:56:10,171 [INFO] -----------------------Build complete-----------------------
2025-03-20 03:56:25,062 [INFO] -----------------------Starting build-----------------------
2025-03-20 03:56:25,082 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-03-20 03:56:25,087 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-03-20 03:56:25,092 [INFO] Running config postbuild_0_ics_api_nodejs
2025-03-20 03:56:43,418 [INFO] Command populate_creds succeeded
2025-03-20 03:56:43,424 [INFO] Running config postbuild_1_ics_api_nodejs
2025-03-20 03:56:44,079 [INFO] Command fetch_key succeeded
2025-03-20 03:56:44,119 [INFO] Command update_key succeeded
2025-03-20 03:56:44,124 [INFO] Running config postbuild_2_ics_api_nodejs
2025-03-20 03:56:44,609 [INFO] Command 01-install succeeded
2025-03-20 03:56:48,054 [INFO] Command 02-activate succeeded
2025-03-20 03:56:48,056 [INFO] ConfigSets completed
2025-03-20 03:56:48,056 [INFO] -----------------------Build complete-----------------------
2025-03-21 11:24:12,401 [INFO] -----------------------Starting build-----------------------
2025-03-21 11:24:12,409 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-03-21 11:24:12,413 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-03-21 11:24:12,416 [INFO] Running config prebuild_0_ics_api_nodejs
2025-03-21 11:24:14,610 [INFO] Command 00_remove_legacy_scripts succeeded
2025-03-21 11:24:16,601 [INFO] Command 01_install_dependencies succeeded
2025-03-21 11:24:17,418 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-21 11:24:17,419 [INFO] ignoreErrors set to true, continuing build
2025-03-21 11:24:18,004 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-21 11:24:18,004 [INFO] ignoreErrors set to true, continuing build
2025-03-21 11:24:18,432 [INFO] Command 03_get_hsm_certificate succeeded
2025-03-21 11:24:18,975 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-03-21 11:24:19,018 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-03-21 11:24:19,138 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-03-21 11:24:19,157 [INFO] Command 07_start_cloudhsm_client succeeded
2025-03-21 11:24:20,028 [INFO] Command 08_define_hsm_credentials succeeded
2025-03-21 11:24:20,513 [INFO] Command 09_download_cryptokitool succeeded
2025-03-21 11:24:20,517 [INFO] Running config prebuild_1_ics_api_nodejs
2025-03-21 11:24:20,526 [INFO] Running config prebuild_2_ics_api_nodejs
2025-03-21 11:24:20,535 [INFO] Running config prebuild_3_ics_api_nodejs
2025-03-21 11:24:20,861 [INFO] Running config prebuild_4_ics_api_nodejs
2025-03-21 11:24:20,907 [INFO] Command 01-create-yum-repo succeeded
2025-03-21 11:24:21,298 [INFO] Command 02-update-yum-cache succeeded
2025-03-21 11:24:22,934 [INFO] Command 03-install-newrelicinfra succeeded
2025-03-21 11:24:22,958 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-03-21 11:24:24,056 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-03-21 11:24:24,061 [INFO] Running config prebuild_5_ics_api_nodejs
2025-03-21 11:24:24,073 [INFO] Running config prebuild_6_ics_api_nodejs
2025-03-21 11:24:24,083 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-03-21 11:24:24,313 [INFO] Running config prebuild_7_ics_api_nodejs
2025-03-21 11:24:24,315 [INFO] ConfigSets completed
2025-03-21 11:24:24,316 [INFO] -----------------------Build complete-----------------------
2025-03-21 11:24:38,713 [INFO] -----------------------Starting build-----------------------
2025-03-21 11:24:38,722 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-03-21 11:24:38,726 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-03-21 11:24:38,731 [INFO] Running config postbuild_0_ics_api_nodejs
2025-03-21 11:24:49,484 [INFO] Command populate_creds succeeded
2025-03-21 11:24:49,491 [INFO] Running config postbuild_1_ics_api_nodejs
2025-03-21 11:24:49,978 [INFO] Command fetch_key succeeded
2025-03-21 11:24:49,996 [INFO] Command update_key succeeded
2025-03-21 11:24:49,999 [INFO] Running config postbuild_2_ics_api_nodejs
2025-03-21 11:24:50,305 [INFO] Command 01-install succeeded
2025-03-21 11:24:53,705 [INFO] Command 02-activate succeeded
2025-03-21 11:24:53,707 [INFO] ConfigSets completed
2025-03-21 11:24:53,707 [INFO] -----------------------Build complete-----------------------
2025-03-24 10:19:30,535 [INFO] -----------------------Starting build-----------------------
2025-03-24 10:19:30,542 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-03-24 10:19:30,546 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-03-24 10:19:30,550 [INFO] Running config prebuild_0_ics_api_nodejs
2025-03-24 10:19:32,767 [INFO] Command 00_remove_legacy_scripts succeeded
2025-03-24 10:19:34,694 [INFO] Command 01_install_dependencies succeeded
2025-03-24 10:19:35,466 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-24 10:19:35,466 [INFO] ignoreErrors set to true, continuing build
2025-03-24 10:19:36,048 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-24 10:19:36,048 [INFO] ignoreErrors set to true, continuing build
2025-03-24 10:19:36,467 [INFO] Command 03_get_hsm_certificate succeeded
2025-03-24 10:19:37,025 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-03-24 10:19:37,068 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-03-24 10:19:37,165 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-03-24 10:19:37,191 [INFO] Command 07_start_cloudhsm_client succeeded
2025-03-24 10:19:38,036 [INFO] Command 08_define_hsm_credentials succeeded
2025-03-24 10:19:38,566 [INFO] Command 09_download_cryptokitool succeeded
2025-03-24 10:19:38,571 [INFO] Running config prebuild_1_ics_api_nodejs
2025-03-24 10:19:38,581 [INFO] Running config prebuild_2_ics_api_nodejs
2025-03-24 10:19:38,590 [INFO] Running config prebuild_3_ics_api_nodejs
2025-03-24 10:19:38,922 [INFO] Running config prebuild_4_ics_api_nodejs
2025-03-24 10:19:38,986 [INFO] Command 01-create-yum-repo succeeded
2025-03-24 10:19:39,389 [INFO] Command 02-update-yum-cache succeeded
2025-03-24 10:19:41,075 [INFO] Command 03-install-newrelicinfra succeeded
2025-03-24 10:19:41,104 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-03-24 10:19:42,233 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-03-24 10:19:42,241 [INFO] Running config prebuild_5_ics_api_nodejs
2025-03-24 10:19:42,248 [INFO] Running config prebuild_6_ics_api_nodejs
2025-03-24 10:19:42,260 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-03-24 10:19:42,561 [INFO] Running config prebuild_7_ics_api_nodejs
2025-03-24 10:19:42,564 [INFO] ConfigSets completed
2025-03-24 10:19:42,564 [INFO] -----------------------Build complete-----------------------
2025-03-24 10:19:56,428 [INFO] -----------------------Starting build-----------------------
2025-03-24 10:19:56,434 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-03-24 10:19:56,437 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-03-24 10:19:56,440 [INFO] Running config postbuild_0_ics_api_nodejs
2025-03-24 10:20:07,737 [INFO] Command populate_creds succeeded
2025-03-24 10:20:07,741 [INFO] Running config postbuild_1_ics_api_nodejs
2025-03-24 10:20:08,137 [INFO] Command fetch_key succeeded
2025-03-24 10:20:08,155 [INFO] Command update_key succeeded
2025-03-24 10:20:08,158 [INFO] Running config postbuild_2_ics_api_nodejs
2025-03-24 10:20:08,450 [INFO] Command 01-install succeeded
2025-03-24 10:20:13,275 [INFO] Command 02-activate succeeded
2025-03-24 10:20:13,277 [INFO] ConfigSets completed
2025-03-24 10:20:13,277 [INFO] -----------------------Build complete-----------------------
2025-03-26 05:53:20,569 [INFO] -----------------------Starting build-----------------------
2025-03-26 05:53:20,576 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-03-26 05:53:20,579 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-03-26 05:53:20,583 [INFO] Running config prebuild_0_ics_api_nodejs
2025-03-26 05:53:22,769 [INFO] Command 00_remove_legacy_scripts succeeded
2025-03-26 05:53:24,786 [INFO] Command 01_install_dependencies succeeded
2025-03-26 05:53:25,572 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-26 05:53:25,572 [INFO] ignoreErrors set to true, continuing build
2025-03-26 05:53:26,147 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-26 05:53:26,147 [INFO] ignoreErrors set to true, continuing build
2025-03-26 05:53:26,609 [INFO] Command 03_get_hsm_certificate succeeded
2025-03-26 05:53:27,089 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-03-26 05:53:27,135 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-03-26 05:53:27,240 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-03-26 05:53:27,276 [INFO] Command 07_start_cloudhsm_client succeeded
2025-03-26 05:53:28,136 [INFO] Command 08_define_hsm_credentials succeeded
2025-03-26 05:53:28,613 [INFO] Command 09_download_cryptokitool succeeded
2025-03-26 05:53:28,618 [INFO] Running config prebuild_1_ics_api_nodejs
2025-03-26 05:53:28,628 [INFO] Running config prebuild_2_ics_api_nodejs
2025-03-26 05:53:28,633 [INFO] Running config prebuild_3_ics_api_nodejs
2025-03-26 05:53:28,984 [INFO] Running config prebuild_4_ics_api_nodejs
2025-03-26 05:53:29,037 [INFO] Command 01-create-yum-repo succeeded
2025-03-26 05:53:29,432 [INFO] Command 02-update-yum-cache succeeded
2025-03-26 05:53:31,126 [INFO] Command 03-install-newrelicinfra succeeded
2025-03-26 05:53:31,157 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-03-26 05:53:32,324 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-03-26 05:53:32,330 [INFO] Running config prebuild_5_ics_api_nodejs
2025-03-26 05:53:32,342 [INFO] Running config prebuild_6_ics_api_nodejs
2025-03-26 05:53:32,353 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-03-26 05:53:32,598 [INFO] Running config prebuild_7_ics_api_nodejs
2025-03-26 05:53:32,600 [INFO] ConfigSets completed
2025-03-26 05:53:32,600 [INFO] -----------------------Build complete-----------------------
2025-03-26 05:53:46,503 [INFO] -----------------------Starting build-----------------------
2025-03-26 05:53:46,509 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-03-26 05:53:46,512 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-03-26 05:53:46,515 [INFO] Running config postbuild_0_ics_api_nodejs
2025-03-26 05:53:58,284 [INFO] Command populate_creds succeeded
2025-03-26 05:53:58,288 [INFO] Running config postbuild_1_ics_api_nodejs
2025-03-26 05:53:58,686 [INFO] Command fetch_key succeeded
2025-03-26 05:53:58,704 [INFO] Command update_key succeeded
2025-03-26 05:53:58,708 [INFO] Running config postbuild_2_ics_api_nodejs
2025-03-26 05:53:59,013 [INFO] Command 01-install succeeded
2025-03-26 05:54:03,411 [INFO] Command 02-activate succeeded
2025-03-26 05:54:03,413 [INFO] ConfigSets completed
2025-03-26 05:54:03,413 [INFO] -----------------------Build complete-----------------------
2025-03-26 07:09:19,437 [INFO] -----------------------Starting build-----------------------
2025-03-26 07:09:19,444 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-03-26 07:09:19,447 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-03-26 07:09:19,450 [INFO] Running config prebuild_0_ics_api_nodejs
2025-03-26 07:09:21,451 [INFO] Command 00_remove_legacy_scripts succeeded
2025-03-26 07:09:23,405 [INFO] Command 01_install_dependencies succeeded
2025-03-26 07:09:24,221 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-26 07:09:24,221 [INFO] ignoreErrors set to true, continuing build
2025-03-26 07:09:24,814 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-26 07:09:24,814 [INFO] ignoreErrors set to true, continuing build
2025-03-26 07:09:25,284 [INFO] Command 03_get_hsm_certificate succeeded
2025-03-26 07:09:25,826 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-03-26 07:09:25,869 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-03-26 07:09:25,958 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-03-26 07:09:25,977 [INFO] Command 07_start_cloudhsm_client succeeded
2025-03-26 07:09:26,845 [INFO] Command 08_define_hsm_credentials succeeded
2025-03-26 07:09:27,372 [INFO] Command 09_download_cryptokitool succeeded
2025-03-26 07:09:27,376 [INFO] Running config prebuild_1_ics_api_nodejs
2025-03-26 07:09:27,384 [INFO] Running config prebuild_2_ics_api_nodejs
2025-03-26 07:09:27,394 [INFO] Running config prebuild_3_ics_api_nodejs
2025-03-26 07:09:27,735 [INFO] Running config prebuild_4_ics_api_nodejs
2025-03-26 07:09:27,792 [INFO] Command 01-create-yum-repo succeeded
2025-03-26 07:09:28,198 [INFO] Command 02-update-yum-cache succeeded
2025-03-26 07:09:29,807 [INFO] Command 03-install-newrelicinfra succeeded
2025-03-26 07:09:29,836 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-03-26 07:09:30,972 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-03-26 07:09:30,978 [INFO] Running config prebuild_5_ics_api_nodejs
2025-03-26 07:09:30,990 [INFO] Running config prebuild_6_ics_api_nodejs
2025-03-26 07:09:31,003 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-03-26 07:09:31,266 [INFO] Running config prebuild_7_ics_api_nodejs
2025-03-26 07:09:31,268 [INFO] ConfigSets completed
2025-03-26 07:09:31,268 [INFO] -----------------------Build complete-----------------------
2025-03-26 07:09:45,051 [INFO] -----------------------Starting build-----------------------
2025-03-26 07:09:45,057 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-03-26 07:09:45,060 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-03-26 07:09:45,065 [INFO] Running config postbuild_0_ics_api_nodejs
2025-03-26 07:09:55,338 [INFO] Command populate_creds succeeded
2025-03-26 07:09:55,342 [INFO] Running config postbuild_1_ics_api_nodejs
2025-03-26 07:09:55,757 [INFO] Command fetch_key succeeded
2025-03-26 07:09:55,775 [INFO] Command update_key succeeded
2025-03-26 07:09:55,778 [INFO] Running config postbuild_2_ics_api_nodejs
2025-03-26 07:09:56,077 [INFO] Command 01-install succeeded
2025-03-26 07:10:01,239 [INFO] Command 02-activate succeeded
2025-03-26 07:10:01,242 [INFO] ConfigSets completed
2025-03-26 07:10:01,242 [INFO] -----------------------Build complete-----------------------
2025-03-26 11:36:01,574 [INFO] -----------------------Starting build-----------------------
2025-03-26 11:36:01,581 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-03-26 11:36:01,583 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-03-26 11:36:01,587 [INFO] Running config prebuild_0_ics_api_nodejs
2025-03-26 11:36:03,541 [INFO] Command 00_remove_legacy_scripts succeeded
2025-03-26 11:36:05,451 [INFO] Command 01_install_dependencies succeeded
2025-03-26 11:36:06,229 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-26 11:36:06,229 [INFO] ignoreErrors set to true, continuing build
2025-03-26 11:36:06,817 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-03-26 11:36:06,817 [INFO] ignoreErrors set to true, continuing build
2025-03-26 11:36:07,240 [INFO] Command 03_get_hsm_certificate succeeded
2025-03-26 11:36:07,750 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-03-26 11:36:07,793 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-03-26 11:36:07,888 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-03-26 11:36:07,909 [INFO] Command 07_start_cloudhsm_client succeeded
2025-03-26 11:36:08,748 [INFO] Command 08_define_hsm_credentials succeeded
2025-03-26 11:36:09,255 [INFO] Command 09_download_cryptokitool succeeded
2025-03-26 11:36:09,259 [INFO] Running config prebuild_1_ics_api_nodejs
2025-03-26 11:36:09,266 [INFO] Running config prebuild_2_ics_api_nodejs
2025-03-26 11:36:09,273 [INFO] Running config prebuild_3_ics_api_nodejs
2025-03-26 11:36:09,591 [INFO] Running config prebuild_4_ics_api_nodejs
2025-03-26 11:36:09,644 [INFO] Command 01-create-yum-repo succeeded
2025-03-26 11:36:10,045 [INFO] Command 02-update-yum-cache succeeded
2025-03-26 11:36:11,624 [INFO] Command 03-install-newrelicinfra succeeded
2025-03-26 11:36:11,656 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-03-26 11:36:12,817 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-03-26 11:36:12,825 [INFO] Running config prebuild_5_ics_api_nodejs
2025-03-26 11:36:12,835 [INFO] Running config prebuild_6_ics_api_nodejs
2025-03-26 11:36:12,847 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-03-26 11:36:13,446 [INFO] Running config prebuild_7_ics_api_nodejs
2025-03-26 11:36:13,450 [INFO] ConfigSets completed
2025-03-26 11:36:13,450 [INFO] -----------------------Build complete-----------------------
2025-03-26 11:36:27,084 [INFO] -----------------------Starting build-----------------------
2025-03-26 11:36:27,090 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-03-26 11:36:27,092 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-03-26 11:36:27,095 [INFO] Running config postbuild_0_ics_api_nodejs
2025-03-26 11:36:37,940 [INFO] Command populate_creds succeeded
2025-03-26 11:36:37,944 [INFO] Running config postbuild_1_ics_api_nodejs
2025-03-26 11:36:38,325 [INFO] Command fetch_key succeeded
2025-03-26 11:36:38,343 [INFO] Command update_key succeeded
2025-03-26 11:36:38,347 [INFO] Running config postbuild_2_ics_api_nodejs
2025-03-26 11:36:38,634 [INFO] Command 01-install succeeded
2025-03-26 11:36:43,185 [INFO] Command 02-activate succeeded
2025-03-26 11:36:43,187 [INFO] ConfigSets completed
2025-03-26 11:36:43,187 [INFO] -----------------------Build complete-----------------------
2025-04-01 03:53:57,484 [INFO] -----------------------Starting build-----------------------
2025-04-01 03:53:57,491 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-01 03:53:57,494 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-01 03:53:57,497 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-01 03:54:00,043 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-01 03:54:01,985 [INFO] Command 01_install_dependencies succeeded
2025-04-01 03:54:02,848 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-01 03:54:02,848 [INFO] ignoreErrors set to true, continuing build
2025-04-01 03:54:03,456 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-01 03:54:03,456 [INFO] ignoreErrors set to true, continuing build
2025-04-01 03:54:03,987 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-01 03:54:04,569 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-01 03:54:04,616 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-01 03:54:04,715 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-01 03:54:04,744 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-01 03:54:05,624 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-01 03:54:06,159 [INFO] Command 09_download_cryptokitool succeeded
2025-04-01 03:54:06,163 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-01 03:54:06,171 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-01 03:54:06,179 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-01 03:54:06,540 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-01 03:54:06,588 [INFO] Command 01-create-yum-repo succeeded
2025-04-01 03:54:07,039 [INFO] Command 02-update-yum-cache succeeded
2025-04-01 03:54:12,054 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-01 03:54:12,088 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-01 03:54:12,121 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-01 03:54:12,128 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-01 03:54:12,137 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-01 03:54:12,148 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-01 03:54:12,368 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-01 03:54:12,370 [INFO] ConfigSets completed
2025-04-01 03:54:12,371 [INFO] -----------------------Build complete-----------------------
2025-04-01 03:54:26,773 [INFO] -----------------------Starting build-----------------------
2025-04-01 03:54:26,783 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-01 03:54:26,787 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-01 03:54:26,792 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-01 03:54:37,550 [INFO] Command populate_creds succeeded
2025-04-01 03:54:37,554 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-01 03:54:38,032 [INFO] Command fetch_key succeeded
2025-04-01 03:54:38,052 [INFO] Command update_key succeeded
2025-04-01 03:54:38,056 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-01 03:54:38,352 [INFO] Command 01-install succeeded
2025-04-01 03:54:42,731 [INFO] Command 02-activate succeeded
2025-04-01 03:54:42,733 [INFO] ConfigSets completed
2025-04-01 03:54:42,733 [INFO] -----------------------Build complete-----------------------
2025-04-01 14:31:50,819 [INFO] -----------------------Starting build-----------------------
2025-04-01 14:31:50,826 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-01 14:31:50,829 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-01 14:31:50,832 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-01 14:31:52,828 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-01 14:31:54,781 [INFO] Command 01_install_dependencies succeeded
2025-04-01 14:31:55,570 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-01 14:31:55,570 [INFO] ignoreErrors set to true, continuing build
2025-04-01 14:31:56,124 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-01 14:31:56,124 [INFO] ignoreErrors set to true, continuing build
2025-04-01 14:31:56,544 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-01 14:31:57,047 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-01 14:31:57,091 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-01 14:31:57,175 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-01 14:31:57,192 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-01 14:31:58,021 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-01 14:31:58,506 [INFO] Command 09_download_cryptokitool succeeded
2025-04-01 14:31:58,511 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-01 14:31:58,520 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-01 14:31:58,528 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-01 14:31:58,849 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-01 14:31:58,900 [INFO] Command 01-create-yum-repo succeeded
2025-04-01 14:31:59,301 [INFO] Command 02-update-yum-cache succeeded
2025-04-01 14:32:00,908 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-01 14:32:00,936 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-01 14:32:02,052 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-01 14:32:02,064 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-01 14:32:02,075 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-01 14:32:02,089 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-01 14:32:02,366 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-01 14:32:02,368 [INFO] ConfigSets completed
2025-04-01 14:32:02,369 [INFO] -----------------------Build complete-----------------------
2025-04-01 14:32:17,174 [INFO] -----------------------Starting build-----------------------
2025-04-01 14:32:17,180 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-01 14:32:17,183 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-01 14:32:17,186 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-01 14:32:28,811 [INFO] Command populate_creds succeeded
2025-04-01 14:32:28,818 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-01 14:32:29,373 [INFO] Command fetch_key succeeded
2025-04-01 14:32:29,392 [INFO] Command update_key succeeded
2025-04-01 14:32:29,395 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-01 14:32:29,701 [INFO] Command 01-install succeeded
2025-04-01 14:32:33,958 [INFO] Command 02-activate succeeded
2025-04-01 14:32:33,960 [INFO] ConfigSets completed
2025-04-01 14:32:33,960 [INFO] -----------------------Build complete-----------------------
2025-04-03 06:18:30,957 [INFO] -----------------------Starting build-----------------------
2025-04-03 06:18:30,963 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-03 06:18:30,966 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-03 06:18:30,969 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-03 06:18:33,395 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-03 06:18:35,223 [INFO] Command 01_install_dependencies succeeded
2025-04-03 06:18:36,042 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 06:18:36,042 [INFO] ignoreErrors set to true, continuing build
2025-04-03 06:18:36,689 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 06:18:36,689 [INFO] ignoreErrors set to true, continuing build
2025-04-03 06:18:37,113 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-03 06:18:37,561 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-03 06:18:37,599 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-03 06:18:37,671 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-03 06:18:37,694 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-03 06:18:38,525 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-03 06:18:39,163 [INFO] Command 09_download_cryptokitool succeeded
2025-04-03 06:18:39,168 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-03 06:18:39,184 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-03 06:18:39,197 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-03 06:18:39,531 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-03 06:18:39,598 [INFO] Command 01-create-yum-repo succeeded
2025-04-03 06:18:40,152 [INFO] Command 02-update-yum-cache succeeded
2025-04-03 06:18:45,341 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-03 06:18:45,379 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-03 06:18:45,484 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-03 06:18:45,489 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-03 06:18:45,502 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-03 06:18:45,527 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-03 06:18:45,885 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-03 06:18:45,889 [INFO] ConfigSets completed
2025-04-03 06:18:45,889 [INFO] -----------------------Build complete-----------------------
2025-04-03 06:18:58,587 [INFO] -----------------------Starting build-----------------------
2025-04-03 06:18:58,593 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-03 06:18:58,596 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-03 06:18:58,599 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-03 06:19:09,132 [INFO] Command populate_creds succeeded
2025-04-03 06:19:09,136 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-03 06:19:09,597 [INFO] Command fetch_key succeeded
2025-04-03 06:19:09,617 [INFO] Command update_key succeeded
2025-04-03 06:19:09,621 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-03 06:19:09,939 [INFO] Command 01-install succeeded
2025-04-03 06:19:13,856 [INFO] Command 02-activate succeeded
2025-04-03 06:19:13,858 [INFO] ConfigSets completed
2025-04-03 06:19:13,858 [INFO] -----------------------Build complete-----------------------
2025-04-03 08:44:36,616 [INFO] -----------------------Starting build-----------------------
2025-04-03 08:44:36,622 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-03 08:44:36,625 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-03 08:44:36,628 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-03 08:44:38,626 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-03 08:44:40,409 [INFO] Command 01_install_dependencies succeeded
2025-04-03 08:44:41,273 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 08:44:41,273 [INFO] ignoreErrors set to true, continuing build
2025-04-03 08:44:41,866 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 08:44:41,866 [INFO] ignoreErrors set to true, continuing build
2025-04-03 08:44:42,280 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-03 08:44:42,736 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-03 08:44:42,776 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-03 08:44:42,848 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-03 08:44:42,863 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-03 08:44:43,692 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-03 08:44:44,213 [INFO] Command 09_download_cryptokitool succeeded
2025-04-03 08:44:44,218 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-03 08:44:44,228 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-03 08:44:44,236 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-03 08:44:44,598 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-03 08:44:44,664 [INFO] Command 01-create-yum-repo succeeded
2025-04-03 08:44:45,067 [INFO] Command 02-update-yum-cache succeeded
2025-04-03 08:44:46,647 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-03 08:44:46,677 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-03 08:44:47,868 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-03 08:44:47,872 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-03 08:44:47,883 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-03 08:44:47,897 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-03 08:44:48,159 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-03 08:44:48,161 [INFO] ConfigSets completed
2025-04-03 08:44:48,161 [INFO] -----------------------Build complete-----------------------
2025-04-03 08:45:02,940 [INFO] -----------------------Starting build-----------------------
2025-04-03 08:45:02,946 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-03 08:45:02,950 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-03 08:45:02,953 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-03 08:45:14,295 [INFO] Command populate_creds succeeded
2025-04-03 08:45:14,299 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-03 08:45:14,746 [INFO] Command fetch_key succeeded
2025-04-03 08:45:14,764 [INFO] Command update_key succeeded
2025-04-03 08:45:14,768 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-03 08:45:15,056 [INFO] Command 01-install succeeded
2025-04-03 08:45:19,751 [INFO] Command 02-activate succeeded
2025-04-03 08:45:19,753 [INFO] ConfigSets completed
2025-04-03 08:45:19,753 [INFO] -----------------------Build complete-----------------------
2025-04-03 09:35:42,142 [INFO] -----------------------Starting build-----------------------
2025-04-03 09:35:42,149 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-03 09:35:42,151 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-03 09:35:42,155 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-03 09:35:44,299 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-03 09:35:46,170 [INFO] Command 01_install_dependencies succeeded
2025-04-03 09:35:46,977 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 09:35:46,978 [INFO] ignoreErrors set to true, continuing build
2025-04-03 09:35:47,599 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 09:35:47,599 [INFO] ignoreErrors set to true, continuing build
2025-04-03 09:35:48,049 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-03 09:35:48,523 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-03 09:35:48,580 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-03 09:35:48,661 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-03 09:35:48,681 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-03 09:35:49,679 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-03 09:35:50,209 [INFO] Command 09_download_cryptokitool succeeded
2025-04-03 09:35:50,214 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-03 09:35:50,222 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-03 09:35:50,229 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-03 09:35:50,565 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-03 09:35:50,615 [INFO] Command 01-create-yum-repo succeeded
2025-04-03 09:35:51,022 [INFO] Command 02-update-yum-cache succeeded
2025-04-03 09:35:52,569 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-03 09:35:52,605 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-03 09:35:53,747 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-03 09:35:53,752 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-03 09:35:53,764 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-03 09:35:53,779 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-03 09:35:54,049 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-03 09:35:54,051 [INFO] ConfigSets completed
2025-04-03 09:35:54,052 [INFO] -----------------------Build complete-----------------------
2025-04-03 09:36:07,835 [INFO] -----------------------Starting build-----------------------
2025-04-03 09:36:07,845 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-03 09:36:07,849 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-03 09:36:07,854 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-03 09:36:18,277 [INFO] Command populate_creds succeeded
2025-04-03 09:36:18,281 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-03 09:36:18,676 [INFO] Command fetch_key succeeded
2025-04-03 09:36:18,694 [INFO] Command update_key succeeded
2025-04-03 09:36:18,698 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-03 09:36:18,986 [INFO] Command 01-install succeeded
2025-04-03 09:36:24,814 [INFO] Command 02-activate succeeded
2025-04-03 09:36:24,816 [INFO] ConfigSets completed
2025-04-03 09:36:24,816 [INFO] -----------------------Build complete-----------------------
2025-04-03 10:35:48,258 [INFO] -----------------------Starting build-----------------------
2025-04-03 10:35:48,265 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-03 10:35:48,268 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-03 10:35:48,271 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-03 10:35:50,352 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-03 10:35:52,247 [INFO] Command 01_install_dependencies succeeded
2025-04-03 10:35:53,031 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 10:35:53,031 [INFO] ignoreErrors set to true, continuing build
2025-04-03 10:35:53,622 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 10:35:53,622 [INFO] ignoreErrors set to true, continuing build
2025-04-03 10:35:54,035 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-03 10:35:54,538 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-03 10:35:54,580 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-03 10:35:54,655 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-03 10:35:54,678 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-03 10:35:55,564 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-03 10:35:56,104 [INFO] Command 09_download_cryptokitool succeeded
2025-04-03 10:35:56,109 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-03 10:35:56,118 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-03 10:35:56,124 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-03 10:35:56,445 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-03 10:35:56,503 [INFO] Command 01-create-yum-repo succeeded
2025-04-03 10:35:56,938 [INFO] Command 02-update-yum-cache succeeded
2025-04-03 10:35:58,535 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-03 10:35:58,580 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-03 10:35:59,711 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-03 10:35:59,720 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-03 10:35:59,731 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-03 10:35:59,743 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-03 10:35:59,987 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-03 10:35:59,989 [INFO] ConfigSets completed
2025-04-03 10:35:59,989 [INFO] -----------------------Build complete-----------------------
2025-04-03 10:36:13,408 [INFO] -----------------------Starting build-----------------------
2025-04-03 10:36:13,414 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-03 10:36:13,417 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-03 10:36:13,421 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-03 10:36:23,689 [INFO] Command populate_creds succeeded
2025-04-03 10:36:23,693 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-03 10:36:24,095 [INFO] Command fetch_key succeeded
2025-04-03 10:36:24,112 [INFO] Command update_key succeeded
2025-04-03 10:36:24,116 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-03 10:36:24,409 [INFO] Command 01-install succeeded
2025-04-03 10:36:28,081 [INFO] Command 02-activate succeeded
2025-04-03 10:36:28,083 [INFO] ConfigSets completed
2025-04-03 10:36:28,084 [INFO] -----------------------Build complete-----------------------
2025-04-03 11:27:23,768 [INFO] -----------------------Starting build-----------------------
2025-04-03 11:27:23,774 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-03 11:27:23,777 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-03 11:27:23,780 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-03 11:27:25,829 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-03 11:27:27,658 [INFO] Command 01_install_dependencies succeeded
2025-04-03 11:27:28,440 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 11:27:28,440 [INFO] ignoreErrors set to true, continuing build
2025-04-03 11:27:29,026 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-03 11:27:29,026 [INFO] ignoreErrors set to true, continuing build
2025-04-03 11:27:29,501 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-03 11:27:29,959 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-03 11:27:30,002 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-03 11:27:30,089 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-03 11:27:30,113 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-03 11:27:30,984 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-03 11:27:31,517 [INFO] Command 09_download_cryptokitool succeeded
2025-04-03 11:27:31,522 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-03 11:27:31,529 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-03 11:27:31,538 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-03 11:27:31,850 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-03 11:27:31,896 [INFO] Command 01-create-yum-repo succeeded
2025-04-03 11:27:32,318 [INFO] Command 02-update-yum-cache succeeded
2025-04-03 11:27:33,859 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-03 11:27:33,884 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-03 11:27:35,027 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-03 11:27:35,038 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-03 11:27:35,048 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-03 11:27:35,060 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-03 11:27:35,389 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-03 11:27:35,392 [INFO] ConfigSets completed
2025-04-03 11:27:35,392 [INFO] -----------------------Build complete-----------------------
2025-04-03 11:27:49,123 [INFO] -----------------------Starting build-----------------------
2025-04-03 11:27:49,130 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-03 11:27:49,133 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-03 11:27:49,136 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-03 11:27:59,265 [INFO] Command populate_creds succeeded
2025-04-03 11:27:59,269 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-03 11:27:59,672 [INFO] Command fetch_key succeeded
2025-04-03 11:27:59,689 [INFO] Command update_key succeeded
2025-04-03 11:27:59,693 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-03 11:28:00,020 [INFO] Command 01-install succeeded
2025-04-03 11:28:04,381 [INFO] Command 02-activate succeeded
2025-04-03 11:28:04,383 [INFO] ConfigSets completed
2025-04-03 11:28:04,383 [INFO] -----------------------Build complete-----------------------
2025-04-04 06:38:45,805 [INFO] -----------------------Starting build-----------------------
2025-04-04 06:38:45,812 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-04 06:38:45,815 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-04 06:38:45,818 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-04 06:38:47,850 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-04 06:38:49,762 [INFO] Command 01_install_dependencies succeeded
2025-04-04 06:38:50,554 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-04 06:38:50,554 [INFO] ignoreErrors set to true, continuing build
2025-04-04 06:38:51,154 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-04 06:38:51,154 [INFO] ignoreErrors set to true, continuing build
2025-04-04 06:38:51,581 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-04 06:38:52,219 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-04 06:38:52,263 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-04 06:38:52,374 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-04 06:38:52,395 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-04 06:38:53,243 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-04 06:38:53,793 [INFO] Command 09_download_cryptokitool succeeded
2025-04-04 06:38:53,797 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-04 06:38:53,804 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-04 06:38:53,814 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-04 06:38:54,142 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-04 06:38:54,187 [INFO] Command 01-create-yum-repo succeeded
2025-04-04 06:38:54,580 [INFO] Command 02-update-yum-cache succeeded
2025-04-04 06:38:56,229 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-04 06:38:56,260 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-04 06:38:57,413 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-04 06:38:57,419 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-04 06:38:57,429 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-04 06:38:57,444 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-04 06:38:57,869 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-04 06:38:57,872 [INFO] ConfigSets completed
2025-04-04 06:38:57,872 [INFO] -----------------------Build complete-----------------------
2025-04-04 06:39:12,721 [INFO] -----------------------Starting build-----------------------
2025-04-04 06:39:12,732 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-04 06:39:12,736 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-04 06:39:12,741 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-04 06:39:23,212 [INFO] Command populate_creds succeeded
2025-04-04 06:39:23,216 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-04 06:39:23,589 [INFO] Command fetch_key succeeded
2025-04-04 06:39:23,607 [INFO] Command update_key succeeded
2025-04-04 06:39:23,610 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-04 06:39:23,904 [INFO] Command 01-install succeeded
2025-04-04 06:39:28,276 [INFO] Command 02-activate succeeded
2025-04-04 06:39:28,279 [INFO] ConfigSets completed
2025-04-04 06:39:28,279 [INFO] -----------------------Build complete-----------------------
2025-04-09 03:45:37,225 [INFO] -----------------------Starting build-----------------------
2025-04-09 03:45:37,232 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-09 03:45:37,234 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-09 03:45:37,237 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-09 03:45:39,545 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-09 03:45:41,374 [INFO] Command 01_install_dependencies succeeded
2025-04-09 03:45:42,303 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-09 03:45:42,303 [INFO] ignoreErrors set to true, continuing build
2025-04-09 03:45:42,931 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-09 03:45:42,931 [INFO] ignoreErrors set to true, continuing build
2025-04-09 03:45:43,350 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-09 03:45:43,826 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-09 03:45:43,865 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-09 03:45:43,933 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-09 03:45:43,955 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-09 03:45:44,870 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-09 03:45:45,360 [INFO] Command 09_download_cryptokitool succeeded
2025-04-09 03:45:45,365 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-09 03:45:45,375 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-09 03:45:45,382 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-09 03:45:45,721 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-09 03:45:45,779 [INFO] Command 01-create-yum-repo succeeded
2025-04-09 03:45:46,189 [INFO] Command 02-update-yum-cache succeeded
2025-04-09 03:45:47,806 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-09 03:45:47,846 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-09 03:45:49,041 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-09 03:45:49,052 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-09 03:45:49,065 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-09 03:45:49,076 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-09 03:45:49,271 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-09 03:45:49,273 [INFO] ConfigSets completed
2025-04-09 03:45:49,273 [INFO] -----------------------Build complete-----------------------
2025-04-09 03:46:02,593 [INFO] -----------------------Starting build-----------------------
2025-04-09 03:46:02,603 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-09 03:46:02,606 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-09 03:46:02,609 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-09 03:46:12,697 [INFO] Command populate_creds succeeded
2025-04-09 03:46:12,701 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-09 03:46:13,069 [INFO] Command fetch_key succeeded
2025-04-09 03:46:13,087 [INFO] Command update_key succeeded
2025-04-09 03:46:13,091 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-09 03:46:13,378 [INFO] Command 01-install succeeded
2025-04-09 03:46:17,677 [INFO] Command 02-activate succeeded
2025-04-09 03:46:17,679 [INFO] ConfigSets completed
2025-04-09 03:46:17,679 [INFO] -----------------------Build complete-----------------------
2025-04-14 10:07:34,260 [INFO] -----------------------Starting build-----------------------
2025-04-14 10:07:34,267 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-14 10:07:34,270 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-14 10:07:34,274 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-14 10:07:36,242 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-14 10:07:38,135 [INFO] Command 01_install_dependencies succeeded
2025-04-14 10:07:38,953 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-14 10:07:38,953 [INFO] ignoreErrors set to true, continuing build
2025-04-14 10:07:39,519 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-14 10:07:39,519 [INFO] ignoreErrors set to true, continuing build
2025-04-14 10:07:39,937 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-14 10:07:40,465 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-14 10:07:40,508 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-14 10:07:40,617 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-14 10:07:40,642 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-14 10:07:41,573 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-14 10:07:42,070 [INFO] Command 09_download_cryptokitool succeeded
2025-04-14 10:07:42,075 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-14 10:07:42,082 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-14 10:07:42,094 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-14 10:07:42,410 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-14 10:07:42,461 [INFO] Command 01-create-yum-repo succeeded
2025-04-14 10:07:42,860 [INFO] Command 02-update-yum-cache succeeded
2025-04-14 10:07:44,576 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-14 10:07:44,600 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-14 10:07:45,736 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-14 10:07:45,748 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-14 10:07:45,758 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-14 10:07:45,772 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-14 10:07:46,019 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-14 10:07:46,021 [INFO] ConfigSets completed
2025-04-14 10:07:46,021 [INFO] -----------------------Build complete-----------------------
2025-04-14 10:07:59,848 [INFO] -----------------------Starting build-----------------------
2025-04-14 10:07:59,854 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-14 10:07:59,858 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-14 10:07:59,862 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-14 10:08:09,850 [INFO] Command populate_creds succeeded
2025-04-14 10:08:09,854 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-14 10:08:10,258 [INFO] Command fetch_key succeeded
2025-04-14 10:08:10,276 [INFO] Command update_key succeeded
2025-04-14 10:08:10,280 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-14 10:08:10,604 [INFO] Command 01-install succeeded
2025-04-14 10:08:13,629 [INFO] Command 02-activate succeeded
2025-04-14 10:08:13,631 [INFO] ConfigSets completed
2025-04-14 10:08:13,632 [INFO] -----------------------Build complete-----------------------
2025-04-23 09:11:09,579 [INFO] -----------------------Starting build-----------------------
2025-04-23 09:11:09,587 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-23 09:11:09,590 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-23 09:11:09,594 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-23 09:11:10,580 [ERROR] Yum makecache failed. Output: Loaded plugins: extras_suggestions, langpacks, priorities, update-motd
Not using downloaded newrelic-infra/repomd.xml because it is older than what we have:
  Current   : Tue Apr 15 04:54:16 2025
  Downloaded: Mon Apr  7 06:19:54 2025
https://download.newrelic.com/infrastructure_agent/linux/yum/amazonlinux/2/x86_64/repodata/4f0f255c0f34a995a8e2678c05ecf7636cd5a8af-other.sqlite.bz2: [Errno 14] HTTPS Error 404 - Not Found
Trying other mirror.
https://download.newrelic.com/infrastructure_agent/linux/yum/amazonlinux/2/x86_64/repodata/7d15b8ea951b29e1c1241b25ec37d53279bfd050-filelists.sqlite.bz2: [Errno 14] HTTPS Error 404 - Not Found
Trying other mirror.
https://download.newrelic.com/infrastructure_agent/linux/yum/amazonlinux/2/x86_64/repodata/7d15b8ea951b29e1c1241b25ec37d53279bfd050-filelists.sqlite.bz2: [Errno 14] HTTPS Error 404 - Not Found
Trying other mirror.


 One of the configured repositories failed (New Relic Infrastructure),
 and yum doesn't have enough cached data to continue. At this point the only
 safe thing yum can do is fail. There are a few ways to work "fix" this:

     1. Contact the upstream for the repository and get them to fix the problem.

     2. Reconfigure the baseurl/etc. for the repository, to point to a working
        upstream. This is most often useful if you are using a newer
        distribution release than is supported by the repository (and the
        packages for the previous distribution release still work).

     3. Run the command with the repository temporarily disabled
            yum --disablerepo=newrelic-infra ...

     4. Disable the repository permanently, so yum won't use it by default. Yum
        will then just ignore the repository until you permanently enable it
        again or use --enablerepo for temporary usage:

            yum-config-manager --disable newrelic-infra
        or
            subscription-manager repos --disable=newrelic-infra

     5. Configure the failing repository to be skipped, if it is unavailable.
        Note that yum will try to contact the repo. when it runs most commands,
        so will have to try and fail each time (and thus. yum will be be much
        slower). If it is a very temporary problem though, this is often a nice
        compromise:

            yum-config-manager --save --setopt=newrelic-infra.skip_if_unavailable=true

failure: repodata/7d15b8ea951b29e1c1241b25ec37d53279bfd050-filelists.sqlite.bz2 from newrelic-infra: [Errno 256] No more mirrors to try.
https://download.newrelic.com/infrastructure_agent/linux/yum/amazonlinux/2/x86_64/repodata/7d15b8ea951b29e1c1241b25ec37d53279bfd050-filelists.sqlite.bz2: [Errno 14] HTTPS Error 404 - Not Found

2025-04-23 09:11:10,580 [ERROR] Error encountered during build of prebuild_0_ics_api_nodejs: Could not create yum cache (return code 1)
Traceback (most recent call last):
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 579, in run_config
    CloudFormationCarpenter(config, self._auth_config, self.strict_mode).build(worklog)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 245, in build
    self._auth_config)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/rpm_tools.py", line 53, in apply
    raise ToolError("Could not create yum cache", cache_result.returncode)
cfnbootstrap.construction_errors.ToolError: Could not create yum cache (return code 1)
2025-04-23 09:11:10,583 [ERROR] -----------------------BUILD FAILED!------------------------
2025-04-23 09:11:10,583 [ERROR] Unhandled exception during build: Could not create yum cache (return code 1)
Traceback (most recent call last):
  File "/opt/aws/bin/cfn-init", line 181, in <module>
    worklog.build(metadata, configSets, strict_mode)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 137, in build
    Contractor(metadata, strict_mode).build(configSets, self)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 567, in build
    self.run_config(config, worklog)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 579, in run_config
    CloudFormationCarpenter(config, self._auth_config, self.strict_mode).build(worklog)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 245, in build
    self._auth_config)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/rpm_tools.py", line 53, in apply
    raise ToolError("Could not create yum cache", cache_result.returncode)
cfnbootstrap.construction_errors.ToolError: Could not create yum cache (return code 1)
2025-04-23 09:21:35,843 [INFO] -----------------------Starting build-----------------------
2025-04-23 09:21:35,850 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-23 09:21:35,853 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-23 09:21:35,857 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-23 09:21:36,709 [ERROR] Yum makecache failed. Output: Loaded plugins: extras_suggestions, langpacks, priorities, update-motd
Not using downloaded newrelic-infra/repomd.xml because it is older than what we have:
  Current   : Tue Apr 15 04:54:16 2025
  Downloaded: Mon Apr  7 06:19:54 2025
https://download.newrelic.com/infrastructure_agent/linux/yum/amazonlinux/2/x86_64/repodata/7d15b8ea951b29e1c1241b25ec37d53279bfd050-filelists.sqlite.bz2: [Errno 14] HTTPS Error 404 - Not Found
Trying other mirror.
https://download.newrelic.com/infrastructure_agent/linux/yum/amazonlinux/2/x86_64/repodata/4f0f255c0f34a995a8e2678c05ecf7636cd5a8af-other.sqlite.bz2: [Errno 14] HTTPS Error 404 - Not Found
Trying other mirror.
https://download.newrelic.com/infrastructure_agent/linux/yum/amazonlinux/2/x86_64/repodata/7d15b8ea951b29e1c1241b25ec37d53279bfd050-filelists.sqlite.bz2: [Errno 14] HTTPS Error 404 - Not Found
Trying other mirror.


 One of the configured repositories failed (New Relic Infrastructure),
 and yum doesn't have enough cached data to continue. At this point the only
 safe thing yum can do is fail. There are a few ways to work "fix" this:

     1. Contact the upstream for the repository and get them to fix the problem.

     2. Reconfigure the baseurl/etc. for the repository, to point to a working
        upstream. This is most often useful if you are using a newer
        distribution release than is supported by the repository (and the
        packages for the previous distribution release still work).

     3. Run the command with the repository temporarily disabled
            yum --disablerepo=newrelic-infra ...

     4. Disable the repository permanently, so yum won't use it by default. Yum
        will then just ignore the repository until you permanently enable it
        again or use --enablerepo for temporary usage:

            yum-config-manager --disable newrelic-infra
        or
            subscription-manager repos --disable=newrelic-infra

     5. Configure the failing repository to be skipped, if it is unavailable.
        Note that yum will try to contact the repo. when it runs most commands,
        so will have to try and fail each time (and thus. yum will be be much
        slower). If it is a very temporary problem though, this is often a nice
        compromise:

            yum-config-manager --save --setopt=newrelic-infra.skip_if_unavailable=true

failure: repodata/7d15b8ea951b29e1c1241b25ec37d53279bfd050-filelists.sqlite.bz2 from newrelic-infra: [Errno 256] No more mirrors to try.
https://download.newrelic.com/infrastructure_agent/linux/yum/amazonlinux/2/x86_64/repodata/7d15b8ea951b29e1c1241b25ec37d53279bfd050-filelists.sqlite.bz2: [Errno 14] HTTPS Error 404 - Not Found

2025-04-23 09:21:36,709 [ERROR] Error encountered during build of prebuild_0_ics_api_nodejs: Could not create yum cache (return code 1)
Traceback (most recent call last):
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 579, in run_config
    CloudFormationCarpenter(config, self._auth_config, self.strict_mode).build(worklog)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 245, in build
    self._auth_config)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/rpm_tools.py", line 53, in apply
    raise ToolError("Could not create yum cache", cache_result.returncode)
cfnbootstrap.construction_errors.ToolError: Could not create yum cache (return code 1)
2025-04-23 09:21:36,709 [ERROR] -----------------------BUILD FAILED!------------------------
2025-04-23 09:21:36,709 [ERROR] Unhandled exception during build: Could not create yum cache (return code 1)
Traceback (most recent call last):
  File "/opt/aws/bin/cfn-init", line 181, in <module>
    worklog.build(metadata, configSets, strict_mode)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 137, in build
    Contractor(metadata, strict_mode).build(configSets, self)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 567, in build
    self.run_config(config, worklog)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 579, in run_config
    CloudFormationCarpenter(config, self._auth_config, self.strict_mode).build(worklog)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/construction.py", line 245, in build
    self._auth_config)
  File "/usr/lib/python3.7/site-packages/cfnbootstrap/rpm_tools.py", line 53, in apply
    raise ToolError("Could not create yum cache", cache_result.returncode)
cfnbootstrap.construction_errors.ToolError: Could not create yum cache (return code 1)
2025-04-23 09:34:32,420 [INFO] -----------------------Starting build-----------------------
2025-04-23 09:34:32,430 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-23 09:34:32,434 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-23 09:34:32,440 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-23 09:34:39,817 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-23 09:34:41,732 [INFO] Command 01_install_dependencies succeeded
2025-04-23 09:34:42,578 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-23 09:34:42,578 [INFO] ignoreErrors set to true, continuing build
2025-04-23 09:34:43,179 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-23 09:34:43,180 [INFO] ignoreErrors set to true, continuing build
2025-04-23 09:34:43,589 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-23 09:34:44,051 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-23 09:34:44,090 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-23 09:34:44,173 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-23 09:34:44,195 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-23 09:34:45,125 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-23 09:34:45,663 [INFO] Command 09_download_cryptokitool succeeded
2025-04-23 09:34:45,667 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-23 09:34:45,676 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-23 09:34:45,683 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-23 09:34:46,035 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-23 09:34:46,095 [INFO] Command 01-create-yum-repo succeeded
2025-04-23 09:34:47,107 [INFO] Command 02-update-yum-cache succeeded
2025-04-23 09:34:51,431 [INFO] Command 03-install-newrelicinfra succeeded
2025-04-23 09:34:51,457 [INFO] Command 04-fix-newrelic-log-permissions succeeded
2025-04-23 09:34:52,622 [INFO] Command 05-restart-newrelic-infra-agent succeeded
2025-04-23 09:34:52,630 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-23 09:34:52,637 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-23 09:34:52,647 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-23 09:34:52,845 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-23 09:34:52,847 [INFO] ConfigSets completed
2025-04-23 09:34:52,848 [INFO] -----------------------Build complete-----------------------
2025-04-23 09:35:06,961 [INFO] -----------------------Starting build-----------------------
2025-04-23 09:35:06,967 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-23 09:35:06,970 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-23 09:35:06,973 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-23 09:35:18,289 [INFO] Command populate_creds succeeded
2025-04-23 09:35:18,295 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-23 09:35:18,901 [INFO] Command fetch_key succeeded
2025-04-23 09:35:18,949 [INFO] Command update_key succeeded
2025-04-23 09:35:18,952 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-23 09:35:19,255 [INFO] Command 01-install succeeded
2025-04-23 09:35:23,643 [INFO] Command 02-activate succeeded
2025-04-23 09:35:23,644 [INFO] ConfigSets completed
2025-04-23 09:35:23,644 [INFO] -----------------------Build complete-----------------------
2025-04-23 12:11:09,135 [INFO] -----------------------Starting build-----------------------
2025-04-23 12:11:09,142 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-23 12:11:09,145 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-23 12:11:09,148 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-23 12:11:14,514 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-23 12:11:16,459 [INFO] Command 01_install_dependencies succeeded
2025-04-23 12:11:17,287 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-23 12:11:17,287 [INFO] ignoreErrors set to true, continuing build
2025-04-23 12:11:17,855 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-23 12:11:17,855 [INFO] ignoreErrors set to true, continuing build
2025-04-23 12:11:18,423 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-23 12:11:18,877 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-23 12:11:18,920 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-23 12:11:19,028 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-23 12:11:19,079 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-23 12:11:19,988 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-23 12:11:20,601 [INFO] Command 09_download_cryptokitool succeeded
2025-04-23 12:11:20,607 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-23 12:11:20,622 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-23 12:11:20,641 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-23 12:11:20,973 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-23 12:11:21,019 [INFO] Command 01-create-yum-repo succeeded
2025-04-23 12:11:21,410 [INFO] Command 02-clean-yum-cache succeeded
2025-04-23 12:11:21,958 [INFO] Command 03-update-yum-cache succeeded
2025-04-23 12:11:26,175 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-23 12:11:26,219 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-23 12:11:27,376 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-23 12:11:27,383 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-23 12:11:27,402 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-23 12:11:27,421 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-23 12:11:27,695 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-23 12:11:27,699 [INFO] ConfigSets completed
2025-04-23 12:11:27,699 [INFO] -----------------------Build complete-----------------------
2025-04-23 12:11:41,248 [INFO] -----------------------Starting build-----------------------
2025-04-23 12:11:41,270 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-23 12:11:41,292 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-23 12:11:41,320 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-23 12:11:52,628 [INFO] Command populate_creds succeeded
2025-04-23 12:11:52,632 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-23 12:11:53,279 [INFO] Command fetch_key succeeded
2025-04-23 12:11:53,315 [INFO] Command update_key succeeded
2025-04-23 12:11:53,320 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-23 12:11:53,615 [INFO] Command 01-install succeeded
2025-04-23 12:11:57,253 [INFO] Command 02-activate succeeded
2025-04-23 12:11:57,255 [INFO] ConfigSets completed
2025-04-23 12:11:57,255 [INFO] -----------------------Build complete-----------------------
2025-04-23 14:00:23,165 [INFO] -----------------------Starting build-----------------------
2025-04-23 14:00:23,177 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-23 14:00:23,180 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-23 14:00:23,184 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-23 14:00:28,650 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-23 14:00:30,559 [INFO] Command 01_install_dependencies succeeded
2025-04-23 14:00:31,401 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-23 14:00:31,401 [INFO] ignoreErrors set to true, continuing build
2025-04-23 14:00:32,011 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-23 14:00:32,011 [INFO] ignoreErrors set to true, continuing build
2025-04-23 14:00:32,449 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-23 14:00:32,921 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-23 14:00:32,964 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-23 14:00:33,040 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-23 14:00:33,064 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-23 14:00:33,914 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-23 14:00:34,443 [INFO] Command 09_download_cryptokitool succeeded
2025-04-23 14:00:34,448 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-23 14:00:34,458 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-23 14:00:34,466 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-23 14:00:34,809 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-23 14:00:34,893 [INFO] Command 01-create-yum-repo succeeded
2025-04-23 14:00:35,297 [INFO] Command 02-clean-yum-cache succeeded
2025-04-23 14:00:35,969 [INFO] Command 03-update-yum-cache succeeded
2025-04-23 14:00:40,114 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-23 14:00:40,149 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-23 14:00:41,346 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-23 14:00:41,351 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-23 14:00:41,363 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-23 14:00:41,379 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-23 14:00:41,578 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-23 14:00:41,580 [INFO] ConfigSets completed
2025-04-23 14:00:41,580 [INFO] -----------------------Build complete-----------------------
2025-04-23 14:00:55,395 [INFO] -----------------------Starting build-----------------------
2025-04-23 14:00:55,412 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-23 14:00:55,419 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-23 14:00:55,432 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-23 14:01:07,315 [INFO] Command populate_creds succeeded
2025-04-23 14:01:07,319 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-23 14:01:07,749 [INFO] Command fetch_key succeeded
2025-04-23 14:01:07,782 [INFO] Command update_key succeeded
2025-04-23 14:01:07,786 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-23 14:01:08,155 [INFO] Command 01-install succeeded
2025-04-23 14:01:11,268 [INFO] Command 02-activate succeeded
2025-04-23 14:01:11,270 [INFO] ConfigSets completed
2025-04-23 14:01:11,270 [INFO] -----------------------Build complete-----------------------
2025-04-23 14:15:27,758 [INFO] -----------------------Starting build-----------------------
2025-04-23 14:15:27,765 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-23 14:15:27,767 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-23 14:15:27,771 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-23 14:15:33,275 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-23 14:15:35,100 [INFO] Command 01_install_dependencies succeeded
2025-04-23 14:15:35,938 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-23 14:15:35,938 [INFO] ignoreErrors set to true, continuing build
2025-04-23 14:15:36,518 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-23 14:15:36,518 [INFO] ignoreErrors set to true, continuing build
2025-04-23 14:15:36,934 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-23 14:15:37,393 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-23 14:15:37,432 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-23 14:15:37,507 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-23 14:15:37,527 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-23 14:15:38,387 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-23 14:15:38,864 [INFO] Command 09_download_cryptokitool succeeded
2025-04-23 14:15:38,871 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-23 14:15:38,888 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-23 14:15:38,901 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-23 14:15:39,252 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-23 14:15:39,395 [INFO] Command 01-create-yum-repo succeeded
2025-04-23 14:15:39,809 [INFO] Command 02-clean-yum-cache succeeded
2025-04-23 14:15:40,643 [INFO] Command 03-update-yum-cache succeeded
2025-04-23 14:15:44,930 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-23 14:15:44,964 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-23 14:15:46,090 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-23 14:15:46,098 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-23 14:15:46,106 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-23 14:15:46,124 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-23 14:15:46,307 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-23 14:15:46,310 [INFO] ConfigSets completed
2025-04-23 14:15:46,310 [INFO] -----------------------Build complete-----------------------
2025-04-23 14:15:59,192 [INFO] -----------------------Starting build-----------------------
2025-04-23 14:15:59,209 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-23 14:15:59,219 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-23 14:15:59,226 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-23 14:16:09,311 [INFO] Command populate_creds succeeded
2025-04-23 14:16:09,315 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-23 14:16:09,726 [INFO] Command fetch_key succeeded
2025-04-23 14:16:09,743 [INFO] Command update_key succeeded
2025-04-23 14:16:09,747 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-23 14:16:10,041 [INFO] Command 01-install succeeded
2025-04-23 14:16:14,945 [INFO] Command 02-activate succeeded
2025-04-23 14:16:14,947 [INFO] ConfigSets completed
2025-04-23 14:16:14,950 [INFO] -----------------------Build complete-----------------------
2025-04-25 05:41:10,662 [INFO] -----------------------Starting build-----------------------
2025-04-25 05:41:10,669 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-25 05:41:10,672 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-25 05:41:10,676 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-25 05:41:16,117 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-25 05:41:18,103 [INFO] Command 01_install_dependencies succeeded
2025-04-25 05:41:18,853 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-25 05:41:18,853 [INFO] ignoreErrors set to true, continuing build
2025-04-25 05:41:19,498 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-25 05:41:19,498 [INFO] ignoreErrors set to true, continuing build
2025-04-25 05:41:20,126 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-25 05:41:20,642 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-25 05:41:20,689 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-25 05:41:20,798 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-25 05:41:20,824 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-25 05:41:21,649 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-25 05:41:22,118 [INFO] Command 09_download_cryptokitool succeeded
2025-04-25 05:41:22,123 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-25 05:41:22,130 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-25 05:41:22,136 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-25 05:41:22,474 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-25 05:41:22,520 [INFO] Command 01-create-yum-repo succeeded
2025-04-25 05:41:22,915 [INFO] Command 02-clean-yum-cache succeeded
2025-04-25 05:41:23,620 [INFO] Command 03-update-yum-cache succeeded
2025-04-25 05:41:27,881 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-25 05:41:27,904 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-25 05:41:29,040 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-25 05:41:29,055 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-25 05:41:29,071 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-25 05:41:29,082 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-25 05:41:29,339 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-25 05:41:29,342 [INFO] ConfigSets completed
2025-04-25 05:41:29,343 [INFO] -----------------------Build complete-----------------------
2025-04-25 05:41:43,183 [INFO] -----------------------Starting build-----------------------
2025-04-25 05:41:43,203 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-25 05:41:43,231 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-25 05:41:43,240 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-25 05:41:53,159 [INFO] Command populate_creds succeeded
2025-04-25 05:41:53,163 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-25 05:41:53,550 [INFO] Command fetch_key succeeded
2025-04-25 05:41:53,567 [INFO] Command update_key succeeded
2025-04-25 05:41:53,571 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-25 05:41:53,862 [INFO] Command 01-install succeeded
2025-04-25 05:41:58,692 [INFO] Command 02-activate succeeded
2025-04-25 05:41:58,694 [INFO] ConfigSets completed
2025-04-25 05:41:58,694 [INFO] -----------------------Build complete-----------------------
2025-04-25 06:24:44,531 [INFO] -----------------------Starting build-----------------------
2025-04-25 06:24:44,537 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-25 06:24:44,540 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-25 06:24:44,543 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-25 06:24:50,009 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-25 06:24:51,812 [INFO] Command 01_install_dependencies succeeded
2025-04-25 06:24:52,668 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-25 06:24:52,668 [INFO] ignoreErrors set to true, continuing build
2025-04-25 06:24:53,261 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-25 06:24:53,261 [INFO] ignoreErrors set to true, continuing build
2025-04-25 06:24:53,754 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-25 06:24:54,202 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-25 06:24:54,241 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-25 06:24:54,316 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-25 06:24:54,340 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-25 06:24:55,208 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-25 06:24:55,694 [INFO] Command 09_download_cryptokitool succeeded
2025-04-25 06:24:55,699 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-25 06:24:55,708 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-25 06:24:55,716 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-25 06:24:56,044 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-25 06:24:56,092 [INFO] Command 01-create-yum-repo succeeded
2025-04-25 06:24:56,486 [INFO] Command 02-clean-yum-cache succeeded
2025-04-25 06:24:57,186 [INFO] Command 03-update-yum-cache succeeded
2025-04-25 06:25:01,212 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-25 06:25:01,250 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-25 06:25:02,440 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-25 06:25:02,451 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-25 06:25:02,461 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-25 06:25:02,473 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-25 06:25:02,686 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-25 06:25:02,688 [INFO] ConfigSets completed
2025-04-25 06:25:02,688 [INFO] -----------------------Build complete-----------------------
2025-04-25 06:25:15,853 [INFO] -----------------------Starting build-----------------------
2025-04-25 06:25:15,860 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-25 06:25:15,864 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-25 06:25:15,869 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-25 06:25:25,879 [INFO] Command populate_creds succeeded
2025-04-25 06:25:25,883 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-25 06:25:26,295 [INFO] Command fetch_key succeeded
2025-04-25 06:25:26,320 [INFO] Command update_key succeeded
2025-04-25 06:25:26,324 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-25 06:25:26,644 [INFO] Command 01-install succeeded
2025-04-25 06:25:30,364 [INFO] Command 02-activate succeeded
2025-04-25 06:25:30,365 [INFO] ConfigSets completed
2025-04-25 06:25:30,365 [INFO] -----------------------Build complete-----------------------
2025-04-28 07:48:18,794 [INFO] -----------------------Starting build-----------------------
2025-04-28 07:48:18,800 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-28 07:48:18,804 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-28 07:48:18,808 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-28 07:48:24,379 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-28 07:48:26,563 [INFO] Command 01_install_dependencies succeeded
2025-04-28 07:48:27,331 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-28 07:48:27,331 [INFO] ignoreErrors set to true, continuing build
2025-04-28 07:48:27,968 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-28 07:48:27,968 [INFO] ignoreErrors set to true, continuing build
2025-04-28 07:48:28,386 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-28 07:48:28,915 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-28 07:48:28,964 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-28 07:48:29,066 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-28 07:48:29,088 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-28 07:48:29,908 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-28 07:48:30,425 [INFO] Command 09_download_cryptokitool succeeded
2025-04-28 07:48:30,429 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-28 07:48:30,437 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-28 07:48:30,445 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-28 07:48:30,784 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-28 07:48:30,835 [INFO] Command 01-create-yum-repo succeeded
2025-04-28 07:48:31,384 [INFO] Command 02-clean-yum-cache succeeded
2025-04-28 07:48:32,068 [INFO] Command 03-update-yum-cache succeeded
2025-04-28 07:48:36,090 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-28 07:48:36,128 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-28 07:48:37,332 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-28 07:48:37,338 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-28 07:48:37,348 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-28 07:48:37,361 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-28 07:48:37,556 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-28 07:48:37,559 [INFO] ConfigSets completed
2025-04-28 07:48:37,559 [INFO] -----------------------Build complete-----------------------
2025-04-28 07:48:51,387 [INFO] -----------------------Starting build-----------------------
2025-04-28 07:48:51,431 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-28 07:48:51,443 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-28 07:48:51,453 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-28 07:49:02,024 [INFO] Command populate_creds succeeded
2025-04-28 07:49:02,028 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-28 07:49:02,416 [INFO] Command fetch_key succeeded
2025-04-28 07:49:02,435 [INFO] Command update_key succeeded
2025-04-28 07:49:02,438 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-28 07:49:02,728 [INFO] Command 01-install succeeded
2025-04-28 07:49:06,383 [INFO] Command 02-activate succeeded
2025-04-28 07:49:06,385 [INFO] ConfigSets completed
2025-04-28 07:49:06,385 [INFO] -----------------------Build complete-----------------------
2025-04-28 10:34:25,267 [INFO] -----------------------Starting build-----------------------
2025-04-28 10:34:25,273 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-28 10:34:25,276 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-28 10:34:25,280 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-28 10:34:30,623 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-28 10:34:32,476 [INFO] Command 01_install_dependencies succeeded
2025-04-28 10:34:33,373 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-28 10:34:33,373 [INFO] ignoreErrors set to true, continuing build
2025-04-28 10:34:33,954 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-28 10:34:33,954 [INFO] ignoreErrors set to true, continuing build
2025-04-28 10:34:34,380 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-28 10:34:34,921 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-28 10:34:34,961 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-28 10:34:35,059 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-28 10:34:35,110 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-28 10:34:35,939 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-28 10:34:36,451 [INFO] Command 09_download_cryptokitool succeeded
2025-04-28 10:34:36,455 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-28 10:34:36,463 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-28 10:34:36,472 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-28 10:34:36,800 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-28 10:34:36,849 [INFO] Command 01-create-yum-repo succeeded
2025-04-28 10:34:37,271 [INFO] Command 02-clean-yum-cache succeeded
2025-04-28 10:34:37,900 [INFO] Command 03-update-yum-cache succeeded
2025-04-28 10:34:42,148 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-28 10:34:42,180 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-28 10:34:43,345 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-28 10:34:43,354 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-28 10:34:43,372 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-28 10:34:43,387 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-28 10:34:43,596 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-28 10:34:43,598 [INFO] ConfigSets completed
2025-04-28 10:34:43,598 [INFO] -----------------------Build complete-----------------------
2025-04-28 10:34:59,547 [INFO] -----------------------Starting build-----------------------
2025-04-28 10:34:59,554 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-28 10:34:59,557 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-28 10:34:59,560 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-28 10:35:09,226 [INFO] Command populate_creds succeeded
2025-04-28 10:35:09,230 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-28 10:35:09,644 [INFO] Command fetch_key succeeded
2025-04-28 10:35:09,663 [INFO] Command update_key succeeded
2025-04-28 10:35:09,666 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-28 10:35:09,976 [INFO] Command 01-install succeeded
2025-04-28 10:35:14,307 [INFO] Command 02-activate succeeded
2025-04-28 10:35:14,309 [INFO] ConfigSets completed
2025-04-28 10:35:14,310 [INFO] -----------------------Build complete-----------------------
2025-04-29 14:37:02,792 [INFO] -----------------------Starting build-----------------------
2025-04-29 14:37:02,798 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-29 14:37:02,801 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-29 14:37:02,804 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-29 14:37:08,304 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-29 14:37:10,264 [INFO] Command 01_install_dependencies succeeded
2025-04-29 14:37:11,031 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-29 14:37:11,031 [INFO] ignoreErrors set to true, continuing build
2025-04-29 14:37:11,649 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-29 14:37:11,649 [INFO] ignoreErrors set to true, continuing build
2025-04-29 14:37:12,102 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-29 14:37:12,581 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-29 14:37:12,622 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-29 14:37:12,712 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-29 14:37:12,726 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-29 14:37:13,575 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-29 14:37:14,112 [INFO] Command 09_download_cryptokitool succeeded
2025-04-29 14:37:14,120 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-29 14:37:14,139 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-29 14:37:14,156 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-29 14:37:14,556 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-29 14:37:14,612 [INFO] Command 01-create-yum-repo succeeded
2025-04-29 14:37:15,037 [INFO] Command 02-clean-yum-cache succeeded
2025-04-29 14:37:15,568 [INFO] Command 03-update-yum-cache succeeded
2025-04-29 14:37:19,717 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-29 14:37:19,740 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-29 14:37:20,889 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-29 14:37:20,895 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-29 14:37:20,908 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-29 14:37:20,924 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-29 14:37:21,156 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-29 14:37:21,162 [INFO] ConfigSets completed
2025-04-29 14:37:21,162 [INFO] -----------------------Build complete-----------------------
2025-04-29 14:37:36,861 [INFO] -----------------------Starting build-----------------------
2025-04-29 14:37:36,866 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-29 14:37:36,869 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-29 14:37:36,872 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-29 14:37:47,740 [INFO] Command populate_creds succeeded
2025-04-29 14:37:47,744 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-29 14:37:48,111 [INFO] Command fetch_key succeeded
2025-04-29 14:37:48,128 [INFO] Command update_key succeeded
2025-04-29 14:37:48,132 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-29 14:37:48,423 [INFO] Command 01-install succeeded
2025-04-29 14:37:53,128 [INFO] Command 02-activate succeeded
2025-04-29 14:37:53,130 [INFO] ConfigSets completed
2025-04-29 14:37:53,130 [INFO] -----------------------Build complete-----------------------
2025-04-30 02:11:29,795 [INFO] -----------------------Starting build-----------------------
2025-04-30 02:11:29,802 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-30 02:11:29,805 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-30 02:11:29,809 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-30 02:11:35,532 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-30 02:11:37,464 [INFO] Command 01_install_dependencies succeeded
2025-04-30 02:11:38,269 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-30 02:11:38,269 [INFO] ignoreErrors set to true, continuing build
2025-04-30 02:11:38,880 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-30 02:11:38,880 [INFO] ignoreErrors set to true, continuing build
2025-04-30 02:11:39,290 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-30 02:11:39,727 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-30 02:11:39,766 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-30 02:11:39,853 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-30 02:11:39,877 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-30 02:11:40,783 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-30 02:11:41,309 [INFO] Command 09_download_cryptokitool succeeded
2025-04-30 02:11:41,314 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-30 02:11:41,326 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-30 02:11:41,331 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-30 02:11:41,687 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-30 02:11:41,739 [INFO] Command 01-create-yum-repo succeeded
2025-04-30 02:11:42,141 [INFO] Command 02-clean-yum-cache succeeded
2025-04-30 02:11:42,781 [INFO] Command 03-update-yum-cache succeeded
2025-04-30 02:11:47,028 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-30 02:11:47,062 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-30 02:11:48,248 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-30 02:11:48,255 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-30 02:11:48,266 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-30 02:11:48,282 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-30 02:11:48,611 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-30 02:11:48,613 [INFO] ConfigSets completed
2025-04-30 02:11:48,613 [INFO] -----------------------Build complete-----------------------
2025-04-30 02:12:04,244 [INFO] -----------------------Starting build-----------------------
2025-04-30 02:12:04,274 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-30 02:12:04,297 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-30 02:12:04,339 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-30 02:12:15,408 [INFO] Command populate_creds succeeded
2025-04-30 02:12:15,415 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-30 02:12:15,857 [INFO] Command fetch_key succeeded
2025-04-30 02:12:15,874 [INFO] Command update_key succeeded
2025-04-30 02:12:15,878 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-30 02:12:16,167 [INFO] Command 01-install succeeded
2025-04-30 02:12:20,256 [INFO] Command 02-activate succeeded
2025-04-30 02:12:20,257 [INFO] ConfigSets completed
2025-04-30 02:12:20,257 [INFO] -----------------------Build complete-----------------------
2025-04-30 08:53:54,511 [INFO] -----------------------Starting build-----------------------
2025-04-30 08:53:54,518 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-04-30 08:53:54,521 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-04-30 08:53:54,524 [INFO] Running config prebuild_0_ics_api_nodejs
2025-04-30 08:53:59,997 [INFO] Command 00_remove_legacy_scripts succeeded
2025-04-30 08:54:01,902 [INFO] Command 01_install_dependencies succeeded
2025-04-30 08:54:02,691 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-30 08:54:02,691 [INFO] ignoreErrors set to true, continuing build
2025-04-30 08:54:03,352 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-04-30 08:54:03,353 [INFO] ignoreErrors set to true, continuing build
2025-04-30 08:54:03,766 [INFO] Command 03_get_hsm_certificate succeeded
2025-04-30 08:54:04,294 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-04-30 08:54:04,338 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-04-30 08:54:04,436 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-04-30 08:54:04,456 [INFO] Command 07_start_cloudhsm_client succeeded
2025-04-30 08:54:05,295 [INFO] Command 08_define_hsm_credentials succeeded
2025-04-30 08:54:05,811 [INFO] Command 09_download_cryptokitool succeeded
2025-04-30 08:54:05,816 [INFO] Running config prebuild_1_ics_api_nodejs
2025-04-30 08:54:05,826 [INFO] Running config prebuild_2_ics_api_nodejs
2025-04-30 08:54:05,833 [INFO] Running config prebuild_3_ics_api_nodejs
2025-04-30 08:54:06,185 [INFO] Running config prebuild_4_ics_api_nodejs
2025-04-30 08:54:06,235 [INFO] Command 01-create-yum-repo succeeded
2025-04-30 08:54:06,639 [INFO] Command 02-clean-yum-cache succeeded
2025-04-30 08:54:07,320 [INFO] Command 03-update-yum-cache succeeded
2025-04-30 08:54:11,980 [INFO] Command 04-install-newrelicinfra succeeded
2025-04-30 08:54:12,011 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-04-30 08:54:13,189 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-04-30 08:54:13,205 [INFO] Running config prebuild_5_ics_api_nodejs
2025-04-30 08:54:13,218 [INFO] Running config prebuild_6_ics_api_nodejs
2025-04-30 08:54:13,233 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-04-30 08:54:13,459 [INFO] Running config prebuild_7_ics_api_nodejs
2025-04-30 08:54:13,462 [INFO] ConfigSets completed
2025-04-30 08:54:13,462 [INFO] -----------------------Build complete-----------------------
2025-04-30 08:54:27,180 [INFO] -----------------------Starting build-----------------------
2025-04-30 08:54:27,191 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-04-30 08:54:27,195 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-04-30 08:54:27,199 [INFO] Running config postbuild_0_ics_api_nodejs
2025-04-30 08:54:37,944 [INFO] Command populate_creds succeeded
2025-04-30 08:54:37,950 [INFO] Running config postbuild_1_ics_api_nodejs
2025-04-30 08:54:38,354 [INFO] Command fetch_key succeeded
2025-04-30 08:54:38,373 [INFO] Command update_key succeeded
2025-04-30 08:54:38,378 [INFO] Running config postbuild_2_ics_api_nodejs
2025-04-30 08:54:38,680 [INFO] Command 01-install succeeded
2025-04-30 08:54:42,950 [INFO] Command 02-activate succeeded
2025-04-30 08:54:42,951 [INFO] ConfigSets completed
2025-04-30 08:54:42,951 [INFO] -----------------------Build complete-----------------------
2025-05-05 12:09:52,170 [INFO] -----------------------Starting build-----------------------
2025-05-05 12:09:52,176 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-05 12:09:52,179 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-05 12:09:52,182 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-05 12:09:57,682 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-05 12:09:59,573 [INFO] Command 01_install_dependencies succeeded
2025-05-05 12:10:00,484 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-05 12:10:00,484 [INFO] ignoreErrors set to true, continuing build
2025-05-05 12:10:01,038 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-05 12:10:01,039 [INFO] ignoreErrors set to true, continuing build
2025-05-05 12:10:01,588 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-05 12:10:02,091 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-05 12:10:02,131 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-05 12:10:02,217 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-05 12:10:02,234 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-05 12:10:03,092 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-05 12:10:03,637 [INFO] Command 09_download_cryptokitool succeeded
2025-05-05 12:10:03,642 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-05 12:10:03,670 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-05 12:10:03,696 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-05 12:10:04,030 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-05 12:10:04,115 [INFO] Command 01-create-yum-repo succeeded
2025-05-05 12:10:04,540 [INFO] Command 02-clean-yum-cache succeeded
2025-05-05 12:10:05,125 [INFO] Command 03-update-yum-cache succeeded
2025-05-05 12:10:09,709 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-05 12:10:09,736 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-05 12:10:10,897 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-05 12:10:10,908 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-05 12:10:10,922 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-05 12:10:10,945 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-05 12:10:11,211 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-05 12:10:11,214 [INFO] ConfigSets completed
2025-05-05 12:10:11,214 [INFO] -----------------------Build complete-----------------------
2025-05-05 12:10:26,341 [INFO] -----------------------Starting build-----------------------
2025-05-05 12:10:26,346 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-05 12:10:26,349 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-05 12:10:26,351 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-05 12:10:37,172 [INFO] Command populate_creds succeeded
2025-05-05 12:10:37,178 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-05 12:10:37,870 [INFO] Command fetch_key succeeded
2025-05-05 12:10:37,920 [INFO] Command update_key succeeded
2025-05-05 12:10:37,926 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-05 12:10:38,261 [INFO] Command 01-install succeeded
2025-05-05 12:10:41,998 [INFO] Command 02-activate succeeded
2025-05-05 12:10:41,999 [INFO] ConfigSets completed
2025-05-05 12:10:41,999 [INFO] -----------------------Build complete-----------------------
2025-05-06 11:35:26,243 [INFO] -----------------------Starting build-----------------------
2025-05-06 11:35:26,252 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-06 11:35:26,255 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-06 11:35:26,259 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-06 11:35:31,534 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-06 11:35:33,571 [INFO] Command 01_install_dependencies succeeded
2025-05-06 11:35:34,487 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-06 11:35:34,487 [INFO] ignoreErrors set to true, continuing build
2025-05-06 11:35:35,094 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-06 11:35:35,094 [INFO] ignoreErrors set to true, continuing build
2025-05-06 11:35:35,532 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-06 11:35:36,043 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-06 11:35:36,082 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-06 11:35:36,169 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-06 11:35:36,184 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-06 11:35:37,048 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-06 11:35:37,566 [INFO] Command 09_download_cryptokitool succeeded
2025-05-06 11:35:37,571 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-06 11:35:37,579 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-06 11:35:37,588 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-06 11:35:37,926 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-06 11:35:37,984 [INFO] Command 01-create-yum-repo succeeded
2025-05-06 11:35:38,439 [INFO] Command 02-clean-yum-cache succeeded
2025-05-06 11:35:39,035 [INFO] Command 03-update-yum-cache succeeded
2025-05-06 11:35:43,246 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-06 11:35:43,272 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-06 11:35:44,447 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-06 11:35:44,457 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-06 11:35:44,470 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-06 11:35:44,496 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-06 11:35:44,711 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-06 11:35:44,716 [INFO] ConfigSets completed
2025-05-06 11:35:44,717 [INFO] -----------------------Build complete-----------------------
2025-05-06 11:35:58,242 [INFO] -----------------------Starting build-----------------------
2025-05-06 11:35:58,269 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-06 11:35:58,291 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-06 11:35:58,315 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-06 11:36:09,253 [INFO] Command populate_creds succeeded
2025-05-06 11:36:09,258 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-06 11:36:09,654 [INFO] Command fetch_key succeeded
2025-05-06 11:36:09,673 [INFO] Command update_key succeeded
2025-05-06 11:36:09,677 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-06 11:36:09,983 [INFO] Command 01-install succeeded
2025-05-06 11:36:13,586 [INFO] Command 02-activate succeeded
2025-05-06 11:36:13,591 [INFO] ConfigSets completed
2025-05-06 11:36:13,591 [INFO] -----------------------Build complete-----------------------
2025-05-06 13:38:53,419 [INFO] -----------------------Starting build-----------------------
2025-05-06 13:38:53,426 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-06 13:38:53,429 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-06 13:38:53,432 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-06 13:38:58,696 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-06 13:39:00,649 [INFO] Command 01_install_dependencies succeeded
2025-05-06 13:39:01,437 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-06 13:39:01,437 [INFO] ignoreErrors set to true, continuing build
2025-05-06 13:39:02,002 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-06 13:39:02,003 [INFO] ignoreErrors set to true, continuing build
2025-05-06 13:39:02,434 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-06 13:39:02,953 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-06 13:39:02,997 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-06 13:39:03,090 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-06 13:39:03,112 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-06 13:39:03,983 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-06 13:39:04,478 [INFO] Command 09_download_cryptokitool succeeded
2025-05-06 13:39:04,483 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-06 13:39:04,494 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-06 13:39:04,503 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-06 13:39:04,856 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-06 13:39:04,967 [INFO] Command 01-create-yum-repo succeeded
2025-05-06 13:39:05,372 [INFO] Command 02-clean-yum-cache succeeded
2025-05-06 13:39:05,978 [INFO] Command 03-update-yum-cache succeeded
2025-05-06 13:39:10,667 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-06 13:39:10,701 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-06 13:39:11,855 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-06 13:39:11,862 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-06 13:39:11,874 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-06 13:39:11,900 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-06 13:39:12,124 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-06 13:39:12,127 [INFO] ConfigSets completed
2025-05-06 13:39:12,128 [INFO] -----------------------Build complete-----------------------
2025-05-06 13:39:26,896 [INFO] -----------------------Starting build-----------------------
2025-05-06 13:39:26,902 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-06 13:39:26,905 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-06 13:39:26,908 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-06 13:39:36,536 [INFO] Command populate_creds succeeded
2025-05-06 13:39:36,539 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-06 13:39:36,986 [INFO] Command fetch_key succeeded
2025-05-06 13:39:37,040 [INFO] Command update_key succeeded
2025-05-06 13:39:37,046 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-06 13:39:37,625 [INFO] Command 01-install succeeded
2025-05-06 13:39:41,421 [INFO] Command 02-activate succeeded
2025-05-06 13:39:41,423 [INFO] ConfigSets completed
2025-05-06 13:39:41,424 [INFO] -----------------------Build complete-----------------------
2025-05-07 00:41:22,096 [INFO] -----------------------Starting build-----------------------
2025-05-07 00:41:22,103 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-07 00:41:22,105 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-07 00:41:22,109 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-07 00:41:27,708 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-07 00:41:29,587 [INFO] Command 01_install_dependencies succeeded
2025-05-07 00:41:30,436 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-07 00:41:30,437 [INFO] ignoreErrors set to true, continuing build
2025-05-07 00:41:31,038 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-07 00:41:31,038 [INFO] ignoreErrors set to true, continuing build
2025-05-07 00:41:31,527 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-07 00:41:32,017 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-07 00:41:32,057 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-07 00:41:32,137 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-07 00:41:32,164 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-07 00:41:33,155 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-07 00:41:33,637 [INFO] Command 09_download_cryptokitool succeeded
2025-05-07 00:41:33,642 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-07 00:41:33,650 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-07 00:41:33,657 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-07 00:41:33,982 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-07 00:41:34,039 [INFO] Command 01-create-yum-repo succeeded
2025-05-07 00:41:34,440 [INFO] Command 02-clean-yum-cache succeeded
2025-05-07 00:41:34,988 [INFO] Command 03-update-yum-cache succeeded
2025-05-07 00:41:39,444 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-07 00:41:39,475 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-07 00:41:40,689 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-07 00:41:40,696 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-07 00:41:40,705 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-07 00:41:40,718 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-07 00:41:40,997 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-07 00:41:41,000 [INFO] ConfigSets completed
2025-05-07 00:41:41,000 [INFO] -----------------------Build complete-----------------------
2025-05-07 00:41:54,798 [INFO] -----------------------Starting build-----------------------
2025-05-07 00:41:54,831 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-07 00:41:54,858 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-07 00:41:54,884 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-07 00:42:06,152 [INFO] Command populate_creds succeeded
2025-05-07 00:42:06,155 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-07 00:42:06,525 [INFO] Command fetch_key succeeded
2025-05-07 00:42:06,543 [INFO] Command update_key succeeded
2025-05-07 00:42:06,547 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-07 00:42:06,885 [INFO] Command 01-install succeeded
2025-05-07 00:42:10,964 [INFO] Command 02-activate succeeded
2025-05-07 00:42:10,965 [INFO] ConfigSets completed
2025-05-07 00:42:10,966 [INFO] -----------------------Build complete-----------------------
2025-05-07 02:13:19,012 [INFO] -----------------------Starting build-----------------------
2025-05-07 02:13:19,018 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-07 02:13:19,021 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-07 02:13:19,024 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-07 02:13:24,700 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-07 02:13:26,455 [INFO] Command 01_install_dependencies succeeded
2025-05-07 02:13:27,306 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-07 02:13:27,306 [INFO] ignoreErrors set to true, continuing build
2025-05-07 02:13:27,904 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-07 02:13:27,904 [INFO] ignoreErrors set to true, continuing build
2025-05-07 02:13:28,335 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-07 02:13:28,794 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-07 02:13:28,837 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-07 02:13:28,923 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-07 02:13:28,943 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-07 02:13:29,843 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-07 02:13:30,365 [INFO] Command 09_download_cryptokitool succeeded
2025-05-07 02:13:30,371 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-07 02:13:30,383 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-07 02:13:30,398 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-07 02:13:30,713 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-07 02:13:30,760 [INFO] Command 01-create-yum-repo succeeded
2025-05-07 02:13:31,184 [INFO] Command 02-clean-yum-cache succeeded
2025-05-07 02:13:31,775 [INFO] Command 03-update-yum-cache succeeded
2025-05-07 02:13:36,630 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-07 02:13:36,664 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-07 02:13:37,818 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-07 02:13:37,833 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-07 02:13:37,847 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-07 02:13:37,856 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-07 02:13:38,130 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-07 02:13:38,133 [INFO] ConfigSets completed
2025-05-07 02:13:38,133 [INFO] -----------------------Build complete-----------------------
2025-05-07 02:13:51,669 [INFO] -----------------------Starting build-----------------------
2025-05-07 02:13:51,676 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-07 02:13:51,679 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-07 02:13:51,683 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-07 02:14:02,741 [INFO] Command populate_creds succeeded
2025-05-07 02:14:02,745 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-07 02:14:03,113 [INFO] Command fetch_key succeeded
2025-05-07 02:14:03,131 [INFO] Command update_key succeeded
2025-05-07 02:14:03,134 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-07 02:14:03,443 [INFO] Command 01-install succeeded
2025-05-07 02:14:07,332 [INFO] Command 02-activate succeeded
2025-05-07 02:14:07,334 [INFO] ConfigSets completed
2025-05-07 02:14:07,334 [INFO] -----------------------Build complete-----------------------
2025-05-07 10:27:25,890 [INFO] -----------------------Starting build-----------------------
2025-05-07 10:27:25,896 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-07 10:27:25,899 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-07 10:27:25,902 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-07 10:27:31,253 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-07 10:27:33,063 [INFO] Command 01_install_dependencies succeeded
2025-05-07 10:27:33,897 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-07 10:27:33,897 [INFO] ignoreErrors set to true, continuing build
2025-05-07 10:27:34,476 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-07 10:27:34,476 [INFO] ignoreErrors set to true, continuing build
2025-05-07 10:27:34,889 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-07 10:27:35,361 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-07 10:27:35,402 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-07 10:27:35,494 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-07 10:27:35,520 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-07 10:27:36,382 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-07 10:27:36,924 [INFO] Command 09_download_cryptokitool succeeded
2025-05-07 10:27:36,928 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-07 10:27:36,943 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-07 10:27:36,968 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-07 10:27:37,283 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-07 10:27:37,330 [INFO] Command 01-create-yum-repo succeeded
2025-05-07 10:27:37,725 [INFO] Command 02-clean-yum-cache succeeded
2025-05-07 10:27:38,301 [INFO] Command 03-update-yum-cache succeeded
2025-05-07 10:27:42,266 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-07 10:27:42,292 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-07 10:27:43,444 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-07 10:27:43,449 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-07 10:27:43,466 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-07 10:27:43,483 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-07 10:27:43,702 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-07 10:27:43,706 [INFO] ConfigSets completed
2025-05-07 10:27:43,706 [INFO] -----------------------Build complete-----------------------
2025-05-07 10:27:58,543 [INFO] -----------------------Starting build-----------------------
2025-05-07 10:27:58,556 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-07 10:27:58,568 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-07 10:27:58,580 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-07 10:28:09,449 [INFO] Command populate_creds succeeded
2025-05-07 10:28:09,455 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-07 10:28:09,983 [INFO] Command fetch_key succeeded
2025-05-07 10:28:10,001 [INFO] Command update_key succeeded
2025-05-07 10:28:10,005 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-07 10:28:10,306 [INFO] Command 01-install succeeded
2025-05-07 10:28:14,157 [INFO] Command 02-activate succeeded
2025-05-07 10:28:14,158 [INFO] ConfigSets completed
2025-05-07 10:28:14,158 [INFO] -----------------------Build complete-----------------------
2025-05-11 22:34:33,425 [INFO] -----------------------Starting build-----------------------
2025-05-11 22:34:33,431 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-11 22:34:33,434 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-11 22:34:33,437 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-11 22:34:39,131 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-11 22:34:40,965 [INFO] Command 01_install_dependencies succeeded
2025-05-11 22:34:41,862 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-11 22:34:41,862 [INFO] ignoreErrors set to true, continuing build
2025-05-11 22:34:42,523 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-11 22:34:42,524 [INFO] ignoreErrors set to true, continuing build
2025-05-11 22:34:42,970 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-11 22:34:43,426 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-11 22:34:43,465 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-11 22:34:43,548 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-11 22:34:43,565 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-11 22:34:44,411 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-11 22:34:45,240 [INFO] Command 09_download_cryptokitool succeeded
2025-05-11 22:34:45,249 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-11 22:34:45,266 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-11 22:34:45,277 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-11 22:34:45,623 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-11 22:34:45,680 [INFO] Command 01-create-yum-repo succeeded
2025-05-11 22:34:46,081 [INFO] Command 02-clean-yum-cache succeeded
2025-05-11 22:34:46,640 [INFO] Command 03-update-yum-cache succeeded
2025-05-11 22:34:50,727 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-11 22:34:50,765 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-11 22:34:51,943 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-11 22:34:51,954 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-11 22:34:51,967 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-11 22:34:51,977 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-11 22:34:52,219 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-11 22:34:52,222 [INFO] ConfigSets completed
2025-05-11 22:34:52,222 [INFO] -----------------------Build complete-----------------------
2025-05-11 22:35:06,809 [INFO] -----------------------Starting build-----------------------
2025-05-11 22:35:06,818 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-11 22:35:06,822 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-11 22:35:06,827 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-11 22:35:17,928 [INFO] Command populate_creds succeeded
2025-05-11 22:35:17,932 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-11 22:35:18,437 [INFO] Command fetch_key succeeded
2025-05-11 22:35:18,455 [INFO] Command update_key succeeded
2025-05-11 22:35:18,459 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-11 22:35:18,751 [INFO] Command 01-install succeeded
2025-05-11 22:35:23,349 [INFO] Command 02-activate succeeded
2025-05-11 22:35:23,351 [INFO] ConfigSets completed
2025-05-11 22:35:23,351 [INFO] -----------------------Build complete-----------------------
2025-05-15 06:03:24,140 [INFO] -----------------------Starting build-----------------------
2025-05-15 06:03:24,146 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-15 06:03:24,149 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-15 06:03:24,152 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-15 06:03:29,939 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-15 06:03:32,063 [INFO] Command 01_install_dependencies succeeded
2025-05-15 06:03:32,909 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-15 06:03:32,909 [INFO] ignoreErrors set to true, continuing build
2025-05-15 06:03:33,564 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-15 06:03:33,565 [INFO] ignoreErrors set to true, continuing build
2025-05-15 06:03:33,989 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-15 06:03:34,455 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-15 06:03:34,494 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-15 06:03:34,573 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-15 06:03:34,596 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-15 06:03:35,525 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-15 06:03:36,293 [INFO] Command 09_download_cryptokitool succeeded
2025-05-15 06:03:36,300 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-15 06:03:36,314 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-15 06:03:36,322 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-15 06:03:36,654 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-15 06:03:36,773 [INFO] Command 01-create-yum-repo succeeded
2025-05-15 06:03:37,172 [INFO] Command 02-clean-yum-cache succeeded
2025-05-15 06:03:37,740 [INFO] Command 03-update-yum-cache succeeded
2025-05-15 06:03:42,850 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-15 06:03:42,885 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-15 06:03:44,062 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-15 06:03:44,070 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-15 06:03:44,078 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-15 06:03:44,091 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-15 06:03:44,320 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-15 06:03:44,322 [INFO] ConfigSets completed
2025-05-15 06:03:44,322 [INFO] -----------------------Build complete-----------------------
2025-05-15 06:03:58,702 [INFO] -----------------------Starting build-----------------------
2025-05-15 06:03:58,713 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-15 06:03:58,717 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-15 06:03:58,722 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-15 06:04:09,745 [INFO] Command populate_creds succeeded
2025-05-15 06:04:09,749 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-15 06:04:10,161 [INFO] Command fetch_key succeeded
2025-05-15 06:04:10,208 [INFO] Command update_key succeeded
2025-05-15 06:04:10,213 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-15 06:04:10,913 [INFO] Command 01-install succeeded
2025-05-15 06:04:16,202 [INFO] Command 02-activate succeeded
2025-05-15 06:04:16,203 [INFO] ConfigSets completed
2025-05-15 06:04:16,204 [INFO] -----------------------Build complete-----------------------
2025-05-15 14:16:23,392 [INFO] -----------------------Starting build-----------------------
2025-05-15 14:16:23,402 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-15 14:16:23,405 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-15 14:16:23,409 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-15 14:16:29,104 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-15 14:16:30,892 [INFO] Command 01_install_dependencies succeeded
2025-05-15 14:16:31,814 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-15 14:16:31,814 [INFO] ignoreErrors set to true, continuing build
2025-05-15 14:16:32,431 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-15 14:16:32,431 [INFO] ignoreErrors set to true, continuing build
2025-05-15 14:16:32,869 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-15 14:16:33,320 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-15 14:16:33,359 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-15 14:16:33,444 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-15 14:16:33,460 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-15 14:16:34,348 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-15 14:16:34,841 [INFO] Command 09_download_cryptokitool succeeded
2025-05-15 14:16:34,846 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-15 14:16:34,862 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-15 14:16:34,885 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-15 14:16:35,242 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-15 14:16:35,288 [INFO] Command 01-create-yum-repo succeeded
2025-05-15 14:16:35,693 [INFO] Command 02-clean-yum-cache succeeded
2025-05-15 14:16:36,247 [INFO] Command 03-update-yum-cache succeeded
2025-05-15 14:16:40,240 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-15 14:16:40,273 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-15 14:16:41,466 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-15 14:16:41,476 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-15 14:16:41,496 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-15 14:16:41,507 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-15 14:16:41,759 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-15 14:16:41,762 [INFO] ConfigSets completed
2025-05-15 14:16:41,762 [INFO] -----------------------Build complete-----------------------
2025-05-15 14:16:55,301 [INFO] -----------------------Starting build-----------------------
2025-05-15 14:16:55,338 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-15 14:16:55,350 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-15 14:16:55,360 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-15 14:17:07,472 [INFO] Command populate_creds succeeded
2025-05-15 14:17:07,476 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-15 14:17:07,861 [INFO] Command fetch_key succeeded
2025-05-15 14:17:07,879 [INFO] Command update_key succeeded
2025-05-15 14:17:07,882 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-15 14:17:08,173 [INFO] Command 01-install succeeded
2025-05-15 14:17:13,869 [INFO] Command 02-activate succeeded
2025-05-15 14:17:13,870 [INFO] ConfigSets completed
2025-05-15 14:17:13,870 [INFO] -----------------------Build complete-----------------------
2025-05-19 11:51:56,590 [INFO] -----------------------Starting build-----------------------
2025-05-19 11:51:56,596 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-19 11:51:56,599 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-19 11:51:56,602 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-19 11:52:02,138 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-19 11:52:04,166 [INFO] Command 01_install_dependencies succeeded
2025-05-19 11:52:05,093 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-19 11:52:05,093 [INFO] ignoreErrors set to true, continuing build
2025-05-19 11:52:05,673 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-19 11:52:05,673 [INFO] ignoreErrors set to true, continuing build
2025-05-19 11:52:06,111 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-19 11:52:06,661 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-19 11:52:06,706 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-19 11:52:06,799 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-19 11:52:06,826 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-19 11:52:07,626 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-19 11:52:08,169 [INFO] Command 09_download_cryptokitool succeeded
2025-05-19 11:52:08,176 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-19 11:52:08,200 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-19 11:52:08,224 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-19 11:52:08,589 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-19 11:52:08,651 [INFO] Command 01-create-yum-repo succeeded
2025-05-19 11:52:09,099 [INFO] Command 02-clean-yum-cache succeeded
2025-05-19 11:52:09,684 [INFO] Command 03-update-yum-cache succeeded
2025-05-19 11:52:13,941 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-19 11:52:13,972 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-19 11:52:15,068 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-19 11:52:15,076 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-19 11:52:15,092 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-19 11:52:15,106 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-19 11:52:15,338 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-19 11:52:15,341 [INFO] ConfigSets completed
2025-05-19 11:52:15,341 [INFO] -----------------------Build complete-----------------------
2025-05-19 11:52:28,163 [INFO] -----------------------Starting build-----------------------
2025-05-19 11:52:28,184 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-19 11:52:28,194 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-19 11:52:28,205 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-19 11:52:44,148 [INFO] Command populate_creds succeeded
2025-05-19 11:52:44,153 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-19 11:52:44,692 [INFO] Command fetch_key succeeded
2025-05-19 11:52:44,726 [INFO] Command update_key succeeded
2025-05-19 11:52:44,732 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-19 11:52:45,162 [INFO] Command 01-install succeeded
2025-05-19 11:52:50,501 [INFO] Command 02-activate succeeded
2025-05-19 11:52:50,503 [INFO] ConfigSets completed
2025-05-19 11:52:50,503 [INFO] -----------------------Build complete-----------------------
2025-05-21 18:45:55,328 [INFO] -----------------------Starting build-----------------------
2025-05-21 18:45:55,335 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-21 18:45:55,338 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-21 18:45:55,341 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-21 18:46:00,948 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-21 18:46:02,828 [INFO] Command 01_install_dependencies succeeded
2025-05-21 18:46:03,826 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-21 18:46:03,826 [INFO] ignoreErrors set to true, continuing build
2025-05-21 18:46:04,453 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-21 18:46:04,453 [INFO] ignoreErrors set to true, continuing build
2025-05-21 18:46:04,849 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-21 18:46:05,288 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-21 18:46:05,326 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-21 18:46:05,410 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-21 18:46:05,425 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-21 18:46:06,376 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-21 18:46:06,904 [INFO] Command 09_download_cryptokitool succeeded
2025-05-21 18:46:06,909 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-21 18:46:06,920 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-21 18:46:06,929 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-21 18:46:07,314 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-21 18:46:07,361 [INFO] Command 01-create-yum-repo succeeded
2025-05-21 18:46:07,758 [INFO] Command 02-clean-yum-cache succeeded
2025-05-21 18:46:08,313 [INFO] Command 03-update-yum-cache succeeded
2025-05-21 18:46:12,464 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-21 18:46:12,501 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-21 18:46:13,658 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-21 18:46:13,667 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-21 18:46:13,675 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-21 18:46:13,692 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-21 18:46:13,931 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-21 18:46:13,934 [INFO] ConfigSets completed
2025-05-21 18:46:13,934 [INFO] -----------------------Build complete-----------------------
2025-05-21 18:46:29,659 [INFO] -----------------------Starting build-----------------------
2025-05-21 18:46:29,665 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-21 18:46:29,668 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-21 18:46:29,671 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-21 18:46:40,875 [INFO] Command populate_creds succeeded
2025-05-21 18:46:40,879 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-21 18:46:41,240 [INFO] Command fetch_key succeeded
2025-05-21 18:46:41,258 [INFO] Command update_key succeeded
2025-05-21 18:46:41,262 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-21 18:46:41,567 [INFO] Command 01-install succeeded
2025-05-21 18:46:46,473 [INFO] Command 02-activate succeeded
2025-05-21 18:46:46,476 [INFO] ConfigSets completed
2025-05-21 18:46:46,476 [INFO] -----------------------Build complete-----------------------
2025-05-22 04:42:58,703 [INFO] -----------------------Starting build-----------------------
2025-05-22 04:42:58,709 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-22 04:42:58,712 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-22 04:42:58,715 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-22 04:43:04,151 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-22 04:43:06,046 [INFO] Command 01_install_dependencies succeeded
2025-05-22 04:43:06,845 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-22 04:43:06,845 [INFO] ignoreErrors set to true, continuing build
2025-05-22 04:43:07,414 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-22 04:43:07,414 [INFO] ignoreErrors set to true, continuing build
2025-05-22 04:43:07,940 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-22 04:43:08,416 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-22 04:43:08,478 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-22 04:43:08,613 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-22 04:43:08,653 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-22 04:43:09,533 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-22 04:43:10,039 [INFO] Command 09_download_cryptokitool succeeded
2025-05-22 04:43:10,048 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-22 04:43:10,059 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-22 04:43:10,071 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-22 04:43:10,415 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-22 04:43:10,474 [INFO] Command 01-create-yum-repo succeeded
2025-05-22 04:43:10,902 [INFO] Command 02-clean-yum-cache succeeded
2025-05-22 04:43:11,520 [INFO] Command 03-update-yum-cache succeeded
2025-05-22 04:43:15,757 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-22 04:43:15,788 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-22 04:43:16,957 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-22 04:43:16,969 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-22 04:43:16,979 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-22 04:43:17,000 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-22 04:43:17,202 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-22 04:43:17,207 [INFO] ConfigSets completed
2025-05-22 04:43:17,207 [INFO] -----------------------Build complete-----------------------
2025-05-22 04:43:32,027 [INFO] -----------------------Starting build-----------------------
2025-05-22 04:43:32,033 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-22 04:43:32,036 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-22 04:43:32,039 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-22 04:43:43,189 [INFO] Command populate_creds succeeded
2025-05-22 04:43:43,193 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-22 04:43:43,550 [INFO] Command fetch_key succeeded
2025-05-22 04:43:43,568 [INFO] Command update_key succeeded
2025-05-22 04:43:43,572 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-22 04:43:43,866 [INFO] Command 01-install succeeded
2025-05-22 04:43:49,570 [INFO] Command 02-activate succeeded
2025-05-22 04:43:49,572 [INFO] ConfigSets completed
2025-05-22 04:43:49,572 [INFO] -----------------------Build complete-----------------------
2025-05-22 05:04:25,229 [INFO] -----------------------Starting build-----------------------
2025-05-22 05:04:25,235 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-22 05:04:25,238 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-22 05:04:25,242 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-22 05:04:31,216 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-22 05:04:33,383 [INFO] Command 01_install_dependencies succeeded
2025-05-22 05:04:34,108 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-22 05:04:34,108 [INFO] ignoreErrors set to true, continuing build
2025-05-22 05:04:34,613 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-22 05:04:34,614 [INFO] ignoreErrors set to true, continuing build
2025-05-22 05:04:35,013 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-22 05:04:35,513 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-22 05:04:35,557 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-22 05:04:35,662 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-22 05:04:35,685 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-22 05:04:36,508 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-22 05:04:36,968 [INFO] Command 09_download_cryptokitool succeeded
2025-05-22 05:04:36,976 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-22 05:04:36,986 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-22 05:04:37,000 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-22 05:04:37,333 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-22 05:04:37,384 [INFO] Command 01-create-yum-repo succeeded
2025-05-22 05:04:37,792 [INFO] Command 02-clean-yum-cache succeeded
2025-05-22 05:04:38,348 [INFO] Command 03-update-yum-cache succeeded
2025-05-22 05:04:42,969 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-22 05:04:43,003 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-22 05:04:44,109 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-22 05:04:44,125 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-22 05:04:44,137 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-22 05:04:44,151 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-22 05:04:44,323 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-22 05:04:44,326 [INFO] ConfigSets completed
2025-05-22 05:04:44,327 [INFO] -----------------------Build complete-----------------------
2025-05-22 05:04:57,536 [INFO] -----------------------Starting build-----------------------
2025-05-22 05:04:57,545 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-22 05:04:57,548 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-22 05:04:57,553 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-22 05:05:09,267 [INFO] Command populate_creds succeeded
2025-05-22 05:05:09,273 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-22 05:05:09,852 [INFO] Command fetch_key succeeded
2025-05-22 05:05:09,909 [INFO] Command update_key succeeded
2025-05-22 05:05:09,915 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-22 05:05:10,232 [INFO] Command 01-install succeeded
2025-05-22 05:05:13,397 [INFO] Command 02-activate succeeded
2025-05-22 05:05:13,398 [INFO] ConfigSets completed
2025-05-22 05:05:13,399 [INFO] -----------------------Build complete-----------------------
2025-05-22 14:57:33,366 [INFO] -----------------------Starting build-----------------------
2025-05-22 14:57:33,372 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-22 14:57:33,376 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-22 14:57:33,380 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-22 14:57:38,645 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-22 14:57:40,691 [INFO] Command 01_install_dependencies succeeded
2025-05-22 14:57:41,461 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-22 14:57:41,461 [INFO] ignoreErrors set to true, continuing build
2025-05-22 14:57:42,091 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-22 14:57:42,092 [INFO] ignoreErrors set to true, continuing build
2025-05-22 14:57:42,519 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-22 14:57:43,013 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-22 14:57:43,055 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-22 14:57:43,149 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-22 14:57:43,161 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-22 14:57:43,978 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-22 14:57:44,437 [INFO] Command 09_download_cryptokitool succeeded
2025-05-22 14:57:44,442 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-22 14:57:44,461 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-22 14:57:44,481 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-22 14:57:44,813 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-22 14:57:44,920 [INFO] Command 01-create-yum-repo succeeded
2025-05-22 14:57:45,484 [INFO] Command 02-clean-yum-cache succeeded
2025-05-22 14:57:46,244 [INFO] Command 03-update-yum-cache succeeded
2025-05-22 14:57:56,655 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-22 14:57:56,718 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-22 14:57:56,862 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-22 14:57:56,875 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-22 14:57:56,889 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-22 14:57:56,903 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-22 14:57:57,224 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-22 14:57:57,227 [INFO] ConfigSets completed
2025-05-22 14:57:57,228 [INFO] -----------------------Build complete-----------------------
2025-05-22 14:58:11,722 [INFO] -----------------------Starting build-----------------------
2025-05-22 14:58:11,732 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-22 14:58:11,736 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-22 14:58:11,741 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-22 14:58:23,098 [INFO] Command populate_creds succeeded
2025-05-22 14:58:23,104 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-22 14:58:23,515 [INFO] Command fetch_key succeeded
2025-05-22 14:58:23,534 [INFO] Command update_key succeeded
2025-05-22 14:58:23,537 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-22 14:58:23,834 [INFO] Command 01-install succeeded
2025-05-22 14:58:28,898 [INFO] Command 02-activate succeeded
2025-05-22 14:58:28,901 [INFO] ConfigSets completed
2025-05-22 14:58:28,902 [INFO] -----------------------Build complete-----------------------
2025-05-27 10:54:40,930 [INFO] -----------------------Starting build-----------------------
2025-05-27 10:54:40,937 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-05-27 10:54:40,941 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-05-27 10:54:40,944 [INFO] Running config prebuild_0_ics_api_nodejs
2025-05-27 10:54:46,417 [INFO] Command 00_remove_legacy_scripts succeeded
2025-05-27 10:54:48,497 [INFO] Command 01_install_dependencies succeeded
2025-05-27 10:54:49,253 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-27 10:54:49,253 [INFO] ignoreErrors set to true, continuing build
2025-05-27 10:54:49,797 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-05-27 10:54:49,798 [INFO] ignoreErrors set to true, continuing build
2025-05-27 10:54:50,232 [INFO] Command 03_get_hsm_certificate succeeded
2025-05-27 10:54:50,753 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-05-27 10:54:50,799 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-05-27 10:54:50,939 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-05-27 10:54:50,974 [INFO] Command 07_start_cloudhsm_client succeeded
2025-05-27 10:54:51,817 [INFO] Command 08_define_hsm_credentials succeeded
2025-05-27 10:54:52,403 [INFO] Command 09_download_cryptokitool succeeded
2025-05-27 10:54:52,415 [INFO] Running config prebuild_1_ics_api_nodejs
2025-05-27 10:54:52,425 [INFO] Running config prebuild_2_ics_api_nodejs
2025-05-27 10:54:52,440 [INFO] Running config prebuild_3_ics_api_nodejs
2025-05-27 10:54:52,760 [INFO] Running config prebuild_4_ics_api_nodejs
2025-05-27 10:54:52,815 [INFO] Command 01-create-yum-repo succeeded
2025-05-27 10:54:53,213 [INFO] Command 02-clean-yum-cache succeeded
2025-05-27 10:54:53,908 [INFO] Command 03-update-yum-cache succeeded
2025-05-27 10:54:57,976 [INFO] Command 04-install-newrelicinfra succeeded
2025-05-27 10:54:58,004 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-05-27 10:54:59,085 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-05-27 10:54:59,098 [INFO] Running config prebuild_5_ics_api_nodejs
2025-05-27 10:54:59,108 [INFO] Running config prebuild_6_ics_api_nodejs
2025-05-27 10:54:59,121 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-05-27 10:54:59,338 [INFO] Running config prebuild_7_ics_api_nodejs
2025-05-27 10:54:59,340 [INFO] ConfigSets completed
2025-05-27 10:54:59,341 [INFO] -----------------------Build complete-----------------------
2025-05-27 10:55:14,485 [INFO] -----------------------Starting build-----------------------
2025-05-27 10:55:14,496 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-05-27 10:55:14,500 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-05-27 10:55:14,505 [INFO] Running config postbuild_0_ics_api_nodejs
2025-05-27 10:55:26,281 [INFO] Command populate_creds succeeded
2025-05-27 10:55:26,285 [INFO] Running config postbuild_1_ics_api_nodejs
2025-05-27 10:55:26,764 [INFO] Command fetch_key succeeded
2025-05-27 10:55:26,783 [INFO] Command update_key succeeded
2025-05-27 10:55:26,786 [INFO] Running config postbuild_2_ics_api_nodejs
2025-05-27 10:55:27,127 [INFO] Command 01-install succeeded
2025-05-27 10:55:30,540 [INFO] Command 02-activate succeeded
2025-05-27 10:55:30,542 [INFO] ConfigSets completed
2025-05-27 10:55:30,542 [INFO] -----------------------Build complete-----------------------
2025-06-04 02:08:02,827 [INFO] -----------------------Starting build-----------------------
2025-06-04 02:08:02,834 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-06-04 02:08:02,837 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-06-04 02:08:02,841 [INFO] Running config prebuild_0_ics_api_nodejs
2025-06-04 02:08:08,791 [INFO] Command 00_remove_legacy_scripts succeeded
2025-06-04 02:08:10,955 [INFO] Command 01_install_dependencies succeeded
2025-06-04 02:08:11,784 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-04 02:08:11,784 [INFO] ignoreErrors set to true, continuing build
2025-06-04 02:08:12,360 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-04 02:08:12,360 [INFO] ignoreErrors set to true, continuing build
2025-06-04 02:08:12,783 [INFO] Command 03_get_hsm_certificate succeeded
2025-06-04 02:08:13,505 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-06-04 02:08:13,549 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-06-04 02:08:13,675 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-06-04 02:08:13,702 [INFO] Command 07_start_cloudhsm_client succeeded
2025-06-04 02:08:14,647 [INFO] Command 08_define_hsm_credentials succeeded
2025-06-04 02:08:15,273 [INFO] Command 09_download_cryptokitool succeeded
2025-06-04 02:08:15,277 [INFO] Running config prebuild_1_ics_api_nodejs
2025-06-04 02:08:15,286 [INFO] Running config prebuild_2_ics_api_nodejs
2025-06-04 02:08:15,297 [INFO] Running config prebuild_3_ics_api_nodejs
2025-06-04 02:08:15,642 [INFO] Running config prebuild_4_ics_api_nodejs
2025-06-04 02:08:15,728 [INFO] Command 01-create-yum-repo succeeded
2025-06-04 02:08:16,126 [INFO] Command 02-clean-yum-cache succeeded
2025-06-04 02:08:16,848 [INFO] Command 03-update-yum-cache succeeded
2025-06-04 02:08:21,494 [INFO] Command 04-install-newrelicinfra succeeded
2025-06-04 02:08:21,521 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-06-04 02:08:22,696 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-06-04 02:08:22,703 [INFO] Running config prebuild_5_ics_api_nodejs
2025-06-04 02:08:22,723 [INFO] Running config prebuild_6_ics_api_nodejs
2025-06-04 02:08:22,738 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-06-04 02:08:23,047 [INFO] Running config prebuild_7_ics_api_nodejs
2025-06-04 02:08:23,050 [INFO] ConfigSets completed
2025-06-04 02:08:23,051 [INFO] -----------------------Build complete-----------------------
2025-06-04 02:08:36,966 [INFO] -----------------------Starting build-----------------------
2025-06-04 02:08:36,972 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-06-04 02:08:36,975 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-06-04 02:08:36,978 [INFO] Running config postbuild_0_ics_api_nodejs
2025-06-04 02:08:49,088 [INFO] Command populate_creds succeeded
2025-06-04 02:08:49,092 [INFO] Running config postbuild_1_ics_api_nodejs
2025-06-04 02:08:49,463 [INFO] Command fetch_key succeeded
2025-06-04 02:08:49,483 [INFO] Command update_key succeeded
2025-06-04 02:08:49,487 [INFO] Running config postbuild_2_ics_api_nodejs
2025-06-04 02:08:49,936 [INFO] Command 01-install succeeded
2025-06-04 02:08:53,484 [INFO] Command 02-activate succeeded
2025-06-04 02:08:53,486 [INFO] ConfigSets completed
2025-06-04 02:08:53,486 [INFO] -----------------------Build complete-----------------------
2025-06-09 12:45:41,189 [INFO] -----------------------Starting build-----------------------
2025-06-09 12:45:41,196 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-06-09 12:45:41,198 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-06-09 12:45:41,202 [INFO] Running config prebuild_0_ics_api_nodejs
2025-06-09 12:45:46,681 [INFO] Command 00_remove_legacy_scripts succeeded
2025-06-09 12:45:48,685 [INFO] Command 01_install_dependencies succeeded
2025-06-09 12:45:49,464 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-09 12:45:49,464 [INFO] ignoreErrors set to true, continuing build
2025-06-09 12:45:50,016 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-09 12:45:50,016 [INFO] ignoreErrors set to true, continuing build
2025-06-09 12:45:50,432 [INFO] Command 03_get_hsm_certificate succeeded
2025-06-09 12:45:50,888 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-06-09 12:45:50,926 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-06-09 12:45:51,015 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-06-09 12:45:51,046 [INFO] Command 07_start_cloudhsm_client succeeded
2025-06-09 12:45:52,129 [INFO] Command 08_define_hsm_credentials succeeded
2025-06-09 12:45:52,711 [INFO] Command 09_download_cryptokitool succeeded
2025-06-09 12:45:52,721 [INFO] Running config prebuild_1_ics_api_nodejs
2025-06-09 12:45:52,755 [INFO] Running config prebuild_2_ics_api_nodejs
2025-06-09 12:45:52,762 [INFO] Running config prebuild_3_ics_api_nodejs
2025-06-09 12:45:53,075 [INFO] Running config prebuild_4_ics_api_nodejs
2025-06-09 12:45:53,155 [INFO] Command 01-create-yum-repo succeeded
2025-06-09 12:45:53,557 [INFO] Command 02-clean-yum-cache succeeded
2025-06-09 12:45:54,402 [INFO] Command 03-update-yum-cache succeeded
2025-06-09 12:45:58,593 [INFO] Command 04-install-newrelicinfra succeeded
2025-06-09 12:45:58,630 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-06-09 12:45:59,866 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-06-09 12:45:59,875 [INFO] Running config prebuild_5_ics_api_nodejs
2025-06-09 12:45:59,886 [INFO] Running config prebuild_6_ics_api_nodejs
2025-06-09 12:45:59,906 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-06-09 12:46:00,123 [INFO] Running config prebuild_7_ics_api_nodejs
2025-06-09 12:46:00,126 [INFO] ConfigSets completed
2025-06-09 12:46:00,126 [INFO] -----------------------Build complete-----------------------
2025-06-09 12:46:14,156 [INFO] -----------------------Starting build-----------------------
2025-06-09 12:46:14,162 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-06-09 12:46:14,165 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-06-09 12:46:14,168 [INFO] Running config postbuild_0_ics_api_nodejs
2025-06-09 12:46:26,740 [INFO] Command populate_creds succeeded
2025-06-09 12:46:26,746 [INFO] Running config postbuild_1_ics_api_nodejs
2025-06-09 12:46:27,304 [INFO] Command fetch_key succeeded
2025-06-09 12:46:27,323 [INFO] Command update_key succeeded
2025-06-09 12:46:27,327 [INFO] Running config postbuild_2_ics_api_nodejs
2025-06-09 12:46:27,628 [INFO] Command 01-install succeeded
2025-06-09 12:46:32,576 [INFO] Command 02-activate succeeded
2025-06-09 12:46:32,578 [INFO] ConfigSets completed
2025-06-09 12:46:32,578 [INFO] -----------------------Build complete-----------------------
2025-06-12 08:26:59,540 [INFO] -----------------------Starting build-----------------------
2025-06-12 08:26:59,547 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-06-12 08:26:59,550 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-06-12 08:26:59,554 [INFO] Running config prebuild_0_ics_api_nodejs
2025-06-12 08:27:05,650 [INFO] Command 00_remove_legacy_scripts succeeded
2025-06-12 08:27:07,888 [INFO] Command 01_install_dependencies succeeded
2025-06-12 08:27:08,672 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-12 08:27:08,672 [INFO] ignoreErrors set to true, continuing build
2025-06-12 08:27:09,254 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-12 08:27:09,255 [INFO] ignoreErrors set to true, continuing build
2025-06-12 08:27:09,926 [INFO] Command 03_get_hsm_certificate succeeded
2025-06-12 08:27:10,386 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-06-12 08:27:10,425 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-06-12 08:27:10,522 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-06-12 08:27:10,539 [INFO] Command 07_start_cloudhsm_client succeeded
2025-06-12 08:27:11,421 [INFO] Command 08_define_hsm_credentials succeeded
2025-06-12 08:27:11,931 [INFO] Command 09_download_cryptokitool succeeded
2025-06-12 08:27:11,935 [INFO] Running config prebuild_1_ics_api_nodejs
2025-06-12 08:27:11,944 [INFO] Running config prebuild_2_ics_api_nodejs
2025-06-12 08:27:11,951 [INFO] Running config prebuild_3_ics_api_nodejs
2025-06-12 08:27:12,304 [INFO] Running config prebuild_4_ics_api_nodejs
2025-06-12 08:27:12,390 [INFO] Command 01-create-yum-repo succeeded
2025-06-12 08:27:12,895 [INFO] Command 02-clean-yum-cache succeeded
2025-06-12 08:27:14,207 [INFO] Command 03-update-yum-cache succeeded
2025-06-12 08:27:18,381 [INFO] Command 04-install-newrelicinfra succeeded
2025-06-12 08:27:18,407 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-06-12 08:27:19,563 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-06-12 08:27:19,584 [INFO] Running config prebuild_5_ics_api_nodejs
2025-06-12 08:27:19,598 [INFO] Running config prebuild_6_ics_api_nodejs
2025-06-12 08:27:19,611 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-06-12 08:27:20,061 [INFO] Running config prebuild_7_ics_api_nodejs
2025-06-12 08:27:20,063 [INFO] ConfigSets completed
2025-06-12 08:27:20,064 [INFO] -----------------------Build complete-----------------------
2025-06-12 08:27:34,529 [INFO] -----------------------Starting build-----------------------
2025-06-12 08:27:34,535 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-06-12 08:27:34,538 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-06-12 08:27:34,541 [INFO] Running config postbuild_0_ics_api_nodejs
2025-06-12 08:27:45,882 [INFO] Command populate_creds succeeded
2025-06-12 08:27:45,885 [INFO] Running config postbuild_1_ics_api_nodejs
2025-06-12 08:27:46,363 [INFO] Command fetch_key succeeded
2025-06-12 08:27:46,381 [INFO] Command update_key succeeded
2025-06-12 08:27:46,385 [INFO] Running config postbuild_2_ics_api_nodejs
2025-06-12 08:27:46,678 [INFO] Command 01-install succeeded
2025-06-12 08:27:50,590 [INFO] Command 02-activate succeeded
2025-06-12 08:27:50,593 [INFO] ConfigSets completed
2025-06-12 08:27:50,593 [INFO] -----------------------Build complete-----------------------
2025-06-13 06:52:42,893 [INFO] -----------------------Starting build-----------------------
2025-06-13 06:52:42,899 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-06-13 06:52:42,902 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-06-13 06:52:42,905 [INFO] Running config prebuild_0_ics_api_nodejs
2025-06-13 06:52:48,669 [INFO] Command 00_remove_legacy_scripts succeeded
2025-06-13 06:52:50,872 [INFO] Command 01_install_dependencies succeeded
2025-06-13 06:52:51,669 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-13 06:52:51,669 [INFO] ignoreErrors set to true, continuing build
2025-06-13 06:52:52,341 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-13 06:52:52,341 [INFO] ignoreErrors set to true, continuing build
2025-06-13 06:52:52,769 [INFO] Command 03_get_hsm_certificate succeeded
2025-06-13 06:52:53,492 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-06-13 06:52:53,561 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-06-13 06:52:53,691 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-06-13 06:52:53,724 [INFO] Command 07_start_cloudhsm_client succeeded
2025-06-13 06:52:54,645 [INFO] Command 08_define_hsm_credentials succeeded
2025-06-13 06:52:55,124 [INFO] Command 09_download_cryptokitool succeeded
2025-06-13 06:52:55,133 [INFO] Running config prebuild_1_ics_api_nodejs
2025-06-13 06:52:55,148 [INFO] Running config prebuild_2_ics_api_nodejs
2025-06-13 06:52:55,166 [INFO] Running config prebuild_3_ics_api_nodejs
2025-06-13 06:52:55,516 [INFO] Running config prebuild_4_ics_api_nodejs
2025-06-13 06:52:55,604 [INFO] Command 01-create-yum-repo succeeded
2025-06-13 06:52:56,004 [INFO] Command 02-clean-yum-cache succeeded
2025-06-13 06:52:56,777 [INFO] Command 03-update-yum-cache succeeded
2025-06-13 06:53:01,016 [INFO] Command 04-install-newrelicinfra succeeded
2025-06-13 06:53:01,060 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-06-13 06:53:02,253 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-06-13 06:53:02,259 [INFO] Running config prebuild_5_ics_api_nodejs
2025-06-13 06:53:02,267 [INFO] Running config prebuild_6_ics_api_nodejs
2025-06-13 06:53:02,279 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-06-13 06:53:02,528 [INFO] Running config prebuild_7_ics_api_nodejs
2025-06-13 06:53:02,531 [INFO] ConfigSets completed
2025-06-13 06:53:02,531 [INFO] -----------------------Build complete-----------------------
2025-06-13 06:53:17,396 [INFO] -----------------------Starting build-----------------------
2025-06-13 06:53:17,402 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-06-13 06:53:17,405 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-06-13 06:53:17,408 [INFO] Running config postbuild_0_ics_api_nodejs
2025-06-13 06:53:28,495 [INFO] Command populate_creds succeeded
2025-06-13 06:53:28,499 [INFO] Running config postbuild_1_ics_api_nodejs
2025-06-13 06:53:28,864 [INFO] Command fetch_key succeeded
2025-06-13 06:53:28,882 [INFO] Command update_key succeeded
2025-06-13 06:53:28,886 [INFO] Running config postbuild_2_ics_api_nodejs
2025-06-13 06:53:29,279 [INFO] Command 01-install succeeded
2025-06-13 06:53:34,247 [INFO] Command 02-activate succeeded
2025-06-13 06:53:34,248 [INFO] ConfigSets completed
2025-06-13 06:53:34,249 [INFO] -----------------------Build complete-----------------------
2025-06-13 12:38:56,239 [INFO] -----------------------Starting build-----------------------
2025-06-13 12:38:56,248 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-06-13 12:38:56,252 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-06-13 12:38:56,255 [INFO] Running config prebuild_0_ics_api_nodejs
2025-06-13 12:39:01,870 [INFO] Command 00_remove_legacy_scripts succeeded
2025-06-13 12:39:03,780 [INFO] Command 01_install_dependencies succeeded
2025-06-13 12:39:04,562 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-13 12:39:04,562 [INFO] ignoreErrors set to true, continuing build
2025-06-13 12:39:05,149 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-13 12:39:05,149 [INFO] ignoreErrors set to true, continuing build
2025-06-13 12:39:05,732 [INFO] Command 03_get_hsm_certificate succeeded
2025-06-13 12:39:06,189 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-06-13 12:39:06,228 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-06-13 12:39:06,327 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-06-13 12:39:06,362 [INFO] Command 07_start_cloudhsm_client succeeded
2025-06-13 12:39:07,228 [INFO] Command 08_define_hsm_credentials succeeded
2025-06-13 12:39:07,920 [INFO] Command 09_download_cryptokitool succeeded
2025-06-13 12:39:07,925 [INFO] Running config prebuild_1_ics_api_nodejs
2025-06-13 12:39:07,946 [INFO] Running config prebuild_2_ics_api_nodejs
2025-06-13 12:39:07,982 [INFO] Running config prebuild_3_ics_api_nodejs
2025-06-13 12:39:08,329 [INFO] Running config prebuild_4_ics_api_nodejs
2025-06-13 12:39:08,415 [INFO] Command 01-create-yum-repo succeeded
2025-06-13 12:39:08,806 [INFO] Command 02-clean-yum-cache succeeded
2025-06-13 12:39:09,464 [INFO] Command 03-update-yum-cache succeeded
2025-06-13 12:39:13,818 [INFO] Command 04-install-newrelicinfra succeeded
2025-06-13 12:39:13,872 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-06-13 12:39:15,048 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-06-13 12:39:15,060 [INFO] Running config prebuild_5_ics_api_nodejs
2025-06-13 12:39:15,071 [INFO] Running config prebuild_6_ics_api_nodejs
2025-06-13 12:39:15,090 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-06-13 12:39:15,282 [INFO] Running config prebuild_7_ics_api_nodejs
2025-06-13 12:39:15,285 [INFO] ConfigSets completed
2025-06-13 12:39:15,285 [INFO] -----------------------Build complete-----------------------
2025-06-13 12:39:29,911 [INFO] -----------------------Starting build-----------------------
2025-06-13 12:39:29,922 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-06-13 12:39:29,926 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-06-13 12:39:29,931 [INFO] Running config postbuild_0_ics_api_nodejs
2025-06-13 12:39:40,448 [INFO] Command populate_creds succeeded
2025-06-13 12:39:40,452 [INFO] Running config postbuild_1_ics_api_nodejs
2025-06-13 12:39:40,827 [INFO] Command fetch_key succeeded
2025-06-13 12:39:40,846 [INFO] Command update_key succeeded
2025-06-13 12:39:40,849 [INFO] Running config postbuild_2_ics_api_nodejs
2025-06-13 12:39:41,368 [INFO] Command 01-install succeeded
2025-06-13 12:39:45,862 [INFO] Command 02-activate succeeded
2025-06-13 12:39:45,866 [INFO] ConfigSets completed
2025-06-13 12:39:45,867 [INFO] -----------------------Build complete-----------------------
2025-06-15 17:34:15,169 [INFO] -----------------------Starting build-----------------------
2025-06-15 17:34:15,177 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-06-15 17:34:15,181 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-06-15 17:34:15,186 [INFO] Running config prebuild_0_ics_api_nodejs
2025-06-15 17:34:21,259 [INFO] Command 00_remove_legacy_scripts succeeded
2025-06-15 17:34:23,268 [INFO] Command 01_install_dependencies succeeded
2025-06-15 17:34:24,122 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-15 17:34:24,122 [INFO] ignoreErrors set to true, continuing build
2025-06-15 17:34:24,697 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-15 17:34:24,697 [INFO] ignoreErrors set to true, continuing build
2025-06-15 17:34:25,130 [INFO] Command 03_get_hsm_certificate succeeded
2025-06-15 17:34:25,578 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-06-15 17:34:25,617 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-06-15 17:34:25,705 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-06-15 17:34:25,728 [INFO] Command 07_start_cloudhsm_client succeeded
2025-06-15 17:34:26,651 [INFO] Command 08_define_hsm_credentials succeeded
2025-06-15 17:34:27,199 [INFO] Command 09_download_cryptokitool succeeded
2025-06-15 17:34:27,204 [INFO] Running config prebuild_1_ics_api_nodejs
2025-06-15 17:34:27,220 [INFO] Running config prebuild_2_ics_api_nodejs
2025-06-15 17:34:27,233 [INFO] Running config prebuild_3_ics_api_nodejs
2025-06-15 17:34:27,563 [INFO] Running config prebuild_4_ics_api_nodejs
2025-06-15 17:34:27,672 [INFO] Command 01-create-yum-repo succeeded
2025-06-15 17:34:28,073 [INFO] Command 02-clean-yum-cache succeeded
2025-06-15 17:34:28,716 [INFO] Command 03-update-yum-cache succeeded
2025-06-15 17:34:32,886 [INFO] Command 04-install-newrelicinfra succeeded
2025-06-15 17:34:32,916 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-06-15 17:34:34,080 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-06-15 17:34:34,092 [INFO] Running config prebuild_5_ics_api_nodejs
2025-06-15 17:34:34,102 [INFO] Running config prebuild_6_ics_api_nodejs
2025-06-15 17:34:34,114 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-06-15 17:34:34,391 [INFO] Running config prebuild_7_ics_api_nodejs
2025-06-15 17:34:34,395 [INFO] ConfigSets completed
2025-06-15 17:34:34,395 [INFO] -----------------------Build complete-----------------------
2025-06-15 17:34:48,637 [INFO] -----------------------Starting build-----------------------
2025-06-15 17:34:48,650 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-06-15 17:34:48,655 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-06-15 17:34:48,661 [INFO] Running config postbuild_0_ics_api_nodejs
2025-06-15 17:35:01,433 [INFO] Command populate_creds succeeded
2025-06-15 17:35:01,436 [INFO] Running config postbuild_1_ics_api_nodejs
2025-06-15 17:35:01,842 [INFO] Command fetch_key succeeded
2025-06-15 17:35:01,862 [INFO] Command update_key succeeded
2025-06-15 17:35:01,866 [INFO] Running config postbuild_2_ics_api_nodejs
2025-06-15 17:35:02,287 [INFO] Command 01-install succeeded
2025-06-15 17:35:05,266 [INFO] Command 02-activate succeeded
2025-06-15 17:35:05,269 [INFO] ConfigSets completed
2025-06-15 17:35:05,270 [INFO] -----------------------Build complete-----------------------
2025-06-16 14:06:44,922 [INFO] -----------------------Starting build-----------------------
2025-06-16 14:06:44,930 [INFO] Running configSets: Infra-EmbeddedPreBuild
2025-06-16 14:06:44,936 [INFO] Running configSet Infra-EmbeddedPreBuild
2025-06-16 14:06:44,940 [INFO] Running config prebuild_0_ics_api_nodejs
2025-06-16 14:06:50,773 [INFO] Command 00_remove_legacy_scripts succeeded
2025-06-16 14:06:52,802 [INFO] Command 01_install_dependencies succeeded
2025-06-16 14:06:53,589 [ERROR] Command 01_install_hsm_client (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-16 14:06:53,589 [INFO] ignoreErrors set to true, continuing build
2025-06-16 14:06:54,183 [ERROR] Command 02_install_hsm_libraries (if [ -z "$(grep "Amazon Linux 2" /etc/os-release)" ]; then
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL6/cloudhsm-client-pkcs11-3.4.4-1.el6.x86_64.rpm
else
  rpm -U https://s3.amazonaws.com/cloudhsmv2-software/CloudHsmClient/EL$(rpm -E %{rhel})/cloudhsm-client-pkcs11-3.4.4-1.el$(rpm -E %{rhel}).x86_64.rpm
fi
) failed
2025-06-16 14:06:54,183 [INFO] ignoreErrors set to true, continuing build
2025-06-16 14:06:54,601 [INFO] Command 03_get_hsm_certificate succeeded
2025-06-16 14:06:55,062 [INFO] Command 04_connect_cloudhsm-client_to_hsm succeeded
2025-06-16 14:06:55,101 [INFO] Command 05_enable_3des_key_reuse succeeded
2025-06-16 14:06:55,202 [INFO] Command 06_enable_cloudhsm_client succeeded
2025-06-16 14:06:55,251 [INFO] Command 07_start_cloudhsm_client succeeded
2025-06-16 14:06:56,148 [INFO] Command 08_define_hsm_credentials succeeded
2025-06-16 14:06:56,716 [INFO] Command 09_download_cryptokitool succeeded
2025-06-16 14:06:56,721 [INFO] Running config prebuild_1_ics_api_nodejs
2025-06-16 14:06:56,728 [INFO] Running config prebuild_2_ics_api_nodejs
2025-06-16 14:06:56,736 [INFO] Running config prebuild_3_ics_api_nodejs
2025-06-16 14:06:57,071 [INFO] Running config prebuild_4_ics_api_nodejs
2025-06-16 14:06:57,158 [INFO] Command 01-create-yum-repo succeeded
2025-06-16 14:06:57,558 [INFO] Command 02-clean-yum-cache succeeded
2025-06-16 14:06:58,229 [INFO] Command 03-update-yum-cache succeeded
2025-06-16 14:07:03,428 [INFO] Command 04-install-newrelicinfra succeeded
2025-06-16 14:07:03,469 [INFO] Command 05-fix-newrelic-log-permissions succeeded
2025-06-16 14:07:04,689 [INFO] Command 06-restart-newrelic-infra-agent succeeded
2025-06-16 14:07:04,700 [INFO] Running config prebuild_5_ics_api_nodejs
2025-06-16 14:07:04,709 [INFO] Running config prebuild_6_ics_api_nodejs
2025-06-16 14:07:04,719 [WARNING] Falling back to Signature Version 2 as no region was specified in S3 URL
2025-06-16 14:07:05,173 [INFO] Running config prebuild_7_ics_api_nodejs
2025-06-16 14:07:05,175 [INFO] ConfigSets completed
2025-06-16 14:07:05,176 [INFO] -----------------------Build complete-----------------------
2025-06-16 14:07:20,215 [INFO] -----------------------Starting build-----------------------
2025-06-16 14:07:20,226 [INFO] Running configSets: Infra-EmbeddedPostBuild
2025-06-16 14:07:20,230 [INFO] Running configSet Infra-EmbeddedPostBuild
2025-06-16 14:07:20,235 [INFO] Running config postbuild_0_ics_api_nodejs
2025-06-16 14:07:33,003 [INFO] Command populate_creds succeeded
2025-06-16 14:07:33,007 [INFO] Running config postbuild_1_ics_api_nodejs
2025-06-16 14:07:33,454 [INFO] Command fetch_key succeeded
2025-06-16 14:07:33,482 [INFO] Command update_key succeeded
2025-06-16 14:07:33,486 [INFO] Running config postbuild_2_ics_api_nodejs
2025-06-16 14:07:33,803 [INFO] Command 01-install succeeded
2025-06-16 14:07:37,396 [INFO] Command 02-activate succeeded
2025-06-16 14:07:37,398 [INFO] ConfigSets completed
2025-06-16 14:07:37,398 [INFO] -----------------------Build complete-----------------------
